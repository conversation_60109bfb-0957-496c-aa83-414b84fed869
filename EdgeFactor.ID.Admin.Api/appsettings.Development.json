{"ConnectionStrings": {"ConfigurationDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_Dev;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_Dev;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "IdentityDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_USA;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "AdminLogDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_Dev;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "AdminAuditLogDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_Dev;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_ID_Dev;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True"}, "AdminApiConfiguration": {"ApiName": "EdgeFactor.ID Api", "ApiVersion": "v1", "ApiBaseUrl": "https://localhost:44302", "IdentityServerBaseUrl": "https://localhost:44310", "OidcSwaggerUIClientId": "EFAdmin_api_swaggerui", "OidcApiName": "EFAdmin_api", "AdministrationRole": "Admin", "RequireHttpsMetadata": false, "CorsAllowAnyOrigin": true, "CorsAllowOrigins": []}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "AuditLoggingConfiguration": {"Source": "IdentityServer.Admin.Api", "SubjectIdentifierClaim": "sub", "SubjectNameClaim": "name", "ClientIdClaim": "client_id"}, "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "UseClientCredentials": true, "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}, "RegisterConfiguration": {"Enabled": true, "IdentitySource": 1, "EFApiBaseUrl": "https://localhost:5001", "EFBaseUrl": "http://localhost:4200", "Environment": {"Location": "United States", "LocationId": "4E008CFD-B452-40A5-8FBD-3DA3976591F8"}}, "MailTemplates": {"NewRegistration": "d-cbaabf2b318847eb8d4f56133044b968", "NewAccount": "d-f7e83b62b92244d89fa9a584986883d1", "PasswordReset": "d-b7ef3470b749468aad28bd4073c5a569", "EmailVerification": "d-27b8a11c61a54166980b1928daf3ef89", "UserCredentials": "d-f7e83b62b92244d89fa9a584986883d1"}, "Urls": {"TermsUrl": "http://localhost:4200/legal/terms-of-use", "PrivacyUrl": "http://localhost:4200/legal/privacy-policy", "ContactUrl": "https://offers.edgefactor.com/contact-us", "VerificationUrl": "", "returnUrl": "http://localhost:4200/auth-callback/external", "aboutUrl": "https://offers.edgefactor.com/aboutedgefactor"}}