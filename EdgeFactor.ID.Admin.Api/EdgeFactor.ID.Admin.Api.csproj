﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Version>2.0.1</Version>
        <Authors><PERSON></Authors>
        <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
        <UserSecretsId>1cc472a2-4e4b-48ce-846b-5219f71fc643</UserSecretsId>
        <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <DockerfileContext>..\..</DockerfileContext>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.MySql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.OpenIdConnectServer" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="6.0.5" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="6.0.5" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="6.0.11" />

        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
        <PackageReference Include="AutoMapper" Version="10.1.1" />        
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="5.1.1" />
        <PackageReference Include="Skoruba.IdentityServer4.Admin.BusinessLogic" Version="2.1.0" />
        <PackageReference Include="Skoruba.IdentityServer4.Admin.BusinessLogic.Identity" Version="2.1.0" />
        <PackageReference Include="Skoruba.IdentityServer4.Shared.Configuration" Version="2.1.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.3.0" />
        <PackageReference Include="Serilog" Version="2.10.0" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="4.2.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="3.3.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="5.7.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.MySql\EdgeFactor.ID.Admin.EntityFramework.MySql.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.PostgreSQL\EdgeFactor.ID.Admin.EntityFramework.PostgreSQL.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.Shared\EdgeFactor.ID.Admin.EntityFramework.Shared.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.SqlServer\EdgeFactor.ID.Admin.EntityFramework.SqlServer.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Shared\EdgeFactor.ID.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resources\ApiErrorResource.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ApiErrorResource.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
      <Content Update="appsettings.CA-Staging.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.CA.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.Development.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.Staging.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.UAT.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.USA-Staging.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.USA.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="serilog.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Resources\ApiErrorResource.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>ApiErrorResource.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

</Project>



















