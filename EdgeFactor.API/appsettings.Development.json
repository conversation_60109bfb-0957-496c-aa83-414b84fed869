{"ConnectionStrings": {"EdgeFactorDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_Staging;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "EdgeFactorGraphDbConnection": "data source=127.0.0.1,1433;initial catalog=EdgeFactor_Tracking_Staging;persist security info=True;user id=CBOS;password=***********$;TrustServerCertificate=True", "CosmosDb": "DefaultEndpointsProtocol=https;AccountName=edgefactor-cosmos;AccountKey=****************************************************************************************;TableEndpoint=https://edgefactor-cosmos.table.cosmos.azure.com:443/;"}, "ApiConfiguration": {"ApiName": "EdgeFactor Api", "ApiVersion": "v1", "MinMobileVersion": "8.0.0", "ApiBaseUrl": "https://localhost:5001", "IdentityServerBaseUrl": "https://localhost:44310", "SignalRUrl": "https://localhost:5003", "OidcSwaggerUIClientId": "edgefactor_api_swagger", "OidcApiName": "edgefactor_api", "AdministrationRole": "Admin", "RequireHttpsMetadata": false, "CorsAllowAnyOrigin": true, "CorsAllowOrigins": ["http://localhost", "https://localhost", "http://localhost:4200", "https://localhost:5001", "http://localhost:56060", "https://localhost:56060", "https://edgefactor.cbos.co.za", "https://edgefactorapi.cbos.co.za", "https://efuat.cbos.co.za", "https://efuatapi.cbos.co.za", "https://api.edgefactor.com", "https://beta.edgefactor.com", "https://edgefactor.com", "https://app.edgefactor.com", "capacitor://localhost", "ionic://localhost", "https://jobseeker.ar.dev.recengine.org", "https://jobseeker.ar.qa.recengine.org", "https://jobseeker.ar.uat.recengine.org", "https://jobseeker.launch.arkansas.gov"], "UploadFilePath": "C:\\Projects\\EdgeFactor\\content\\uploads", "Html5ContainerPath": "C:\\Projects\\EdgeFactor\\content\\html"}, "Azure": {"AadTenantId": "d8f3f433-94ac-4f91-912d-afa3a67fd673", "AmsAbsoluteUri": "https://edgefactor1.blob.core.windows.net", "AmsAccountKey": "****************************************************************************************", "AmsAccountName": "edgefactor1", "AmsClientId": "410e3f76-030a-4083-9a92-a0625151ae5b", "AmsConnection": "DefaultEndpointsProtocol=https;AccountName=edgefactor1;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AmsSecret": "*************************************", "AmsSubscriptionId": "feae1a56-0fcb-4ed3-a12a-0d30318932b0", "ArmEndpoint": "https://management.azure.com/", "ResourceGroup": "EdgeFactor", "ServiceBusConnection": "Endpoint=sb://edgefactor.servicebus.windows.net/;SharedAccessKeyName=client;SharedAccessKey=PJnE62rgf3wpQ+b2hD1TXiHa9Y0KjiLcnWw0QoyNelE=", "StorageAbsoluteUri": "https://edgefactor.blob.core.windows.net", "StorageAccountKey": "****************************************************************************************", "StorageAccountName": "edgefactor", "StorageBlobContainer": "static", "StorageConnection": "DefaultEndpointsProtocol=https;AccountName=edgefactor;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "Environment": "uat_", "EnableCache": false}, "IdentityServer": {"Authority_CA": "https://localhost:44310", "Authority_USA": "https://localhost:44310", "Audience": "edgefactor", "Secret": "^xk!5BSTd*5QA2AV"}, "IdentityServerClient": {"Authority_CA": "https://localhost:44310", "Authority_USA": "https://localhost:44310", "ApiUrl_CA": "https://localhost:44302", "ApiUrl_USA": "https://localhost:44302", "ClientId": "edgefactor_api", "Secret": "secret", "Scope": "EFAdmin_api"}, "CleverClient": {"Authority": "https://clever.com/", "ApiUrl": "https://api.clever.com/v2.1/", "ClientId": "c778a7bb8ec7e93bee6f", "Secret": "53ddd26946f41e51516423f1103c48fa91675256", "Scope": "profile"}, "ClasslinkClient": {"Authority": "https://launchpad.classlink.com/oauth2/v2/auth", "ApiUrl": "https://nodeapi.classlink.com/v2/", "ClientId": "c1665510553932c3286eb3f8a08abc92008c9630b02b", "Secret": "********************************", "Scope": "profile"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApplicationInsights": {"ConnectionString": "InstrumentationKey=8835c7a9-0a81-4eea-bd5c-69ae8b0c6cd1;IngestionEndpoint=https://southafricanorth-1.in.applicationinsights.azure.com/"}, "SendGridConfiguration": {"ApiKey": "*********************************************************************", "SourceEmail": "<EMAIL>", "SourceName": "EdgeFactor"}, "AppConfiguration": {"AppBaseUrl": "http://localhost:4200", "ContentUrl": "https://edgefactor-byebf9e8csehewga.z01.azurefd.net/v1/"}, "QlikConfiguration": {"ApiKey": "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "AppId": "6c11698a-1745-41d9-ae8b-134c1b2d11b2", "AppUrl": "https://edgefactor.us.qlikcloud.com/", "ClientId": "04f318adea1c742fa79cb06926b329e2", "ClientSecret": "5e953858bac77395e44ff87c440945488d4a181d6cd18e1539dfc7dc9fb2404f", "GrantType": "urn:qlik:oauth:user-impersonation", "Scope": "admin_classic", "UserId": "675986038abaab9718c2e93f"}, "CredentialEngineConfiguration": {"CTID": "ce-4a62628e-c96a-4b05-b8e3-a9e4b845a627", "ApiKey": "1b442fc9-8a12-4d38-8e44-8d97f81dbbd6", "AppUrl": "https://sandbox.credentialengine.org"}}