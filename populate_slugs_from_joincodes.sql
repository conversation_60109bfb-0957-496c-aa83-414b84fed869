-- =====================================================
-- SQL Script to populate slugs from JoinCodes for Instances
-- With transaction safety and verification steps
-- =====================================================

-- Step 1: Begin transaction for safety
BEGIN TRANSACTION;

PRINT 'Starting slug population from JoinCodes...';
PRINT 'Transaction started - changes can be rolled back if needed.';
PRINT '';

-- Step 2: Show current state before changes
PRINT '=== BEFORE UPDATE - Current State ===';
SELECT 
    'Total instances with JoinCode but no Slug' as Description,
    COUNT(*) as Count
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode != ''
  AND (Slug IS NULL OR Slug = '');

SELECT 
    'Instances with duplicate JoinCodes (will be skipped)' as Description,
    COUNT(*) as Count
FROM Instances 
WHERE JoinCode IN (
    SELECT JoinCode
    FROM Instances 
    WHERE JoinCode IS NOT NULL 
      AND JoinCode != ''
      AND (Slug IS NULL OR Slug = '')
    GROUP BY JoinCode
    HAVING COUNT(*) > 1
);

SELECT 
    'JoinCodes that already exist as slugs (will be skipped)' as Description,
    COUNT(*) as Count
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode != ''
  AND (Slug IS NULL OR Slug = '')
  AND JoinCode IN (
    SELECT Slug
    FROM Instances 
    WHERE Slug IS NOT NULL 
      AND Slug != ''
  );

-- Step 3: Show exactly which records will be updated
PRINT '';
PRINT '=== RECORDS THAT WILL BE UPDATED ===';
SELECT 
    'Records eligible for update' as Description,
    COUNT(*) as Count
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode != ''
  AND (Slug IS NULL OR Slug = '')
  AND JoinCode NOT IN (
    -- Exclude JoinCodes that appear multiple times
    SELECT JoinCode
    FROM Instances 
    WHERE JoinCode IS NOT NULL 
      AND JoinCode != ''
      AND (Slug IS NULL OR Slug = '')
    GROUP BY JoinCode
    HAVING COUNT(*) > 1
  )
  AND JoinCode NOT IN (
    -- Exclude JoinCodes that already exist as slugs
    SELECT Slug
    FROM Instances 
    WHERE Slug IS NOT NULL 
      AND Slug != ''
  );

-- Optional: Show sample of records that will be updated (first 10)
PRINT '';
PRINT 'Sample of records that will be updated (first 10):';
SELECT TOP 10
    Id,
    Title,
    JoinCode,
    Slug as CurrentSlug,
    JoinCode as NewSlug
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode != ''
  AND (Slug IS NULL OR Slug = '')
  AND JoinCode NOT IN (
    SELECT JoinCode
    FROM Instances 
    WHERE JoinCode IS NOT NULL 
      AND JoinCode != ''
      AND (Slug IS NULL OR Slug = '')
    GROUP BY JoinCode
    HAVING COUNT(*) > 1
  )
  AND JoinCode NOT IN (
    SELECT Slug
    FROM Instances 
    WHERE Slug IS NOT NULL 
      AND Slug != ''
  )
ORDER BY Title;

PRINT '';
PRINT '=== PERFORMING UPDATE ===';

-- Step 4: Perform the actual update
UPDATE Instances 
SET Slug = JoinCode,
    LastModifiedDate = GETDATE()
WHERE JoinCode IS NOT NULL 
  AND JoinCode != ''
  AND (Slug IS NULL OR Slug = '')
  AND JoinCode NOT IN (
    -- Exclude JoinCodes that appear multiple times
    SELECT JoinCode
    FROM Instances 
    WHERE JoinCode IS NOT NULL 
      AND JoinCode != ''
      AND (Slug IS NULL OR Slug = '')
    GROUP BY JoinCode
    HAVING COUNT(*) > 1
  )
  AND JoinCode NOT IN (
    -- Exclude JoinCodes that already exist as slugs
    SELECT Slug
    FROM Instances 
    WHERE Slug IS NOT NULL 
      AND Slug != ''
  );

-- Step 5: Show results of the update
DECLARE @RowsUpdated INT = @@ROWCOUNT;
PRINT 'Rows updated: ' + CAST(@RowsUpdated AS VARCHAR(10));

-- Step 6: Verification - show updated records
PRINT '';
PRINT '=== VERIFICATION - Updated Records ===';
SELECT 
    Id,
    Title,
    JoinCode,
    Slug,
    LastModifiedDate
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode = Slug
  AND LastModifiedDate >= DATEADD(MINUTE, -5, GETDATE()) -- Records updated in last 5 minutes
ORDER BY LastModifiedDate DESC;

-- Step 7: Final summary
PRINT '';
PRINT '=== FINAL SUMMARY ===';
SELECT 
    'Total instances with matching JoinCode and Slug' as Description,
    COUNT(*) as Count
FROM Instances 
WHERE JoinCode IS NOT NULL 
  AND JoinCode = Slug;

PRINT '';
PRINT '=== TRANSACTION STATUS ===';
PRINT 'Transaction is still open. Review the results above.';
PRINT 'If everything looks correct, run: COMMIT;';
PRINT 'If you want to undo the changes, run: ROLLBACK;';
PRINT '';
PRINT 'IMPORTANT: You must run either COMMIT or ROLLBACK to complete this transaction!';

-- Uncomment ONE of the following lines after reviewing the results:
-- COMMIT;    -- Use this to save the changes permanently
-- ROLLBACK;  -- Use this to undo all changes and restore original state
