const connectionString =
  'InstrumentationKey=941d87e4-51d6-4aa3-b179-edcdc0847d1c;IngestionEndpoint=https://centralus-2.in.applicationinsights.azure.com/;LiveEndpoint=https://centralus.livediagnostics.monitor.azure.com/';
export const environment = {
  production: false,
  version: require('../../package.json').version,
  platform: 'development',
  apiUrl: 'https://localhost:5001/',
  appUrl: 'http://localhost:4200/',
  contentUrl: 'https://ef-staging-f5esbhcphbhcg0fz.z03.azurefd.net/v1/',
  signalRUrl: 'https://localhost:5003/',
  appInsights: {
    connectionString: connectionString,
  },
  client_id: 'edgefactor',
  authority_usa: 'https://localhost:44310',
  authority_ca: 'https://localhost:44310',
  redirect_uri: 'http://localhost:4200/auth-callback',
  post_logout_redirect_uri: 'http://localhost:4200/',
  silent_redirect_uri: 'http://localhost:4200/silent-callback.html',
  convey_this_api_key: '',
  googleMapApiKey: 'AIzaSyAYKflWJabFnpz5QL-4kdUjDL8O9StPnGs',
  html5Path: 'https://app.edgefactor.com/html5',
  clarityId: '',
  showShareBtn: true,
};
