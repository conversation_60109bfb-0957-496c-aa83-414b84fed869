{"ConnectionStrings": {"ConfigurationDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "IdentityDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_USA_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "CertificateConfiguration": {"UseTemporarySigningKeyForDevelopment": true, "CertificateStoreLocation": "LocalMachine", "CertificateValidOnly": true, "UseSigningCertificateThumbprint": false, "SigningCertificateThumbprint": "", "UseSigningCertificatePfxFile": false, "SigningCertificatePfxFilePath": "", "SigningCertificatePfxFilePassword": "", "UseValidationCertificatePfxFile": false, "ValidationCertificatePfxFilePath": "", "ValidationCertificatePfxFilePassword": "", "UseValidationCertificateThumbprint": false, "ValidationCertificateThumbprint": "", "UseSigningCertificateForAzureKeyVault": false, "UseValidationCertificateForAzureKeyVault": false}, "RegisterConfiguration": {"Enabled": true, "IdentitySource": 1, "EFApiBaseUrl": "https://edgefactorapi.cbos.co.za/v1", "EFBaseUrl": "https://edgefactor.cbos.co.za", "Environment": {"Location": "United States", "LocationId": "4E008CFD-B452-40A5-8FBD-3DA3976591F8"}}, "MailTemplates": {"NewRegistration": "d-cbaabf2b318847eb8d4f56133044b968", "NewAccount": "d-f7e83b62b92244d89fa9a584986883d1", "PasswordReset": "d-b7ef3470b749468aad28bd4073c5a569", "EmailVerification": "d-27b8a11c61a54166980b1928daf3ef89", "UserCredentials": "d-f7e83b62b92244d89fa9a584986883d1"}, "Urls": {"TermsUrl": "https://edgefactor.cbos.co.za/legal/terms-of-use", "PrivacyUrl": "https://edgefactor.cbos.co.za/legal/privacy-policy", "ContactUrl": "https://offers.edgefactor.com/contact-us", "VerificationUrl": "", "returnUrl": "https://edgefactor.cbos.co.za/auth-callback/external", "aboutUrl": "https://offers.edgefactor.com/aboutedgefactor"}, "ExternalProvidersConfiguration": {"UseGoogleProvider": true, "GoogleClientId": "************-6o9vpf493knege3kihiq239893urqgfd.apps.googleusercontent.com", "GoogleClientSecret": "1jl5knMFxJD3FgkI-D4CsXXA", "UseFacebookProvider": true, "FacebookClientId": "****************", "FacebookClientSecret": "********************************", "UseMicrosoftProvider": true, "MicrosoftClientId": "0399416e-e982-4ad4-b46d-d28707d27803", "MicrosoftClientSecret": "****************************************", "UseCleverProvider": true, "CleverClientId": "002c5ae988f478b3c9a5", "CleverClientSecret": "9db396986414bbba046c3924683d739393f196d2", "CleverCallbackPath": "/signin-oauth", "UseClassLinkProvider": false, "ClassLinkClientId": "c16655105539316a66c3d6c63b66b46ff343a0c6251f", "ClassLinkClientSecret": "75d9d3f560bfdd693fb8f345c17bbbb1", "ClassLinkCallbackPath": "/signin-oauth", "UseCanvasProvider": false, "CanvasClientId": "", "CanvasClientSecret": "", "CanvasCallbackPath": "/signin-oauth", "UsePowerSchoolProvider": false, "PowerSchoolClientId": "", "PowerSchoolClientSecret": "", "PowerSchoolCallbackPath": "/signin-oauth", "UseD2lProvider": true, "D2lClientId": "12345", "D2lClientSecret": "12345", "D2lCallbackPath": "/signin-oauth", "UseLoginGovProvider": true, "LoginGovId": "urn:gov:gsa:openidconnect.profiles:sp:sso:arkansas:edgefactor", "LoginGovAuthority": "https://secure.login.gov"}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "*********************************************************************", "SourceEmail": "<EMAIL>", "SourceName": "EdgeFactor"}, "LoginConfiguration": {"ResolutionPolicy": "Username"}, "AdminConfiguration": {"PageTitle": "", "HomePageLogoUri": "~/images/EFLogo_White.png", "FaviconUri": "~/favicon.ico", "Theme": null, "CustomThemeCss": "/dist/css/theme.css", "IdentityAdminBaseUrl": "https://edgefactoridadmin.cbos.co.za", "AdministrationRole": "Admin"}, "CspTrustedDomains": ["www.gravatar.com", "fonts.googleapis.com", "fonts.gstatic.com"], "CultureConfiguration": {"Cultures": [], "DefaultCulture": null}, "AdvancedConfiguration": {"IssuerUri": ""}, "BasePath": "", "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": true}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "GitHubCallbackPath": "", "UseClientCredentials": true, "IdentityServerCertificateName": "", "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}}