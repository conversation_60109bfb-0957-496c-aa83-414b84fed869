﻿// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

// Original file: https://github.com/IdentityServer/IdentityServer4.Samples
// Modified by <PERSON>

using EdgeFactor.ID.STS.Identity.Configuration;
using EdgeFactor.ID.STS.Identity.Helpers;
using EdgeFactor.ID.STS.Identity.Helpers.Localization;
using EdgeFactor.ID.STS.Identity.ViewModels.Account;
using EdgeFactor.ID.STS.Identity.ViewModels.Home;
using EdgeFactor.ID.STS.Identity.ViewModels.ServiceBus;
using IdentityModel;
using IdentityServer4;
using IdentityServer4.Events;
using IdentityServer4.Extensions;
using IdentityServer4.Models;
using IdentityServer4.Services;
using IdentityServer4.Stores;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Skoruba.IdentityServer4.Shared.Configuration.Configuration.Identity;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;
using Claim = System.Security.Claims.Claim;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace EdgeFactor.ID.STS.Identity.Controllers
{
    [SecurityHeaders]
    [Authorize]
    public class AccountController<TUser, TKey> : Controller
        where TUser : IdentityUser<TKey>, new()
        where TKey : IEquatable<TKey>
    {
        private readonly UserResolver<TUser> _userResolver;
        private readonly UserManager<TUser> _userManager;
        private readonly ApplicationSignInManager<TUser> _signInManager;
        private readonly IIdentityServerInteractionService _interaction;
        private readonly IClientStore _clientStore;
        private readonly IAuthenticationSchemeProvider _schemeProvider;
        private readonly IEventService _events;
        private readonly IEmailSender _emailSender;
        private readonly IGenericControllerLocalizer<AccountController<TUser, TKey>> _localizer;
        private readonly LoginConfiguration _loginConfiguration;
        private readonly RegisterConfiguration _registerConfiguration;
        private readonly IdentityOptions _identityOptions;
        private readonly ILogger<AccountController<TUser, TKey>> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;

        public AccountController(
            UserResolver<TUser> userResolver,
            UserManager<TUser> userManager,
            ApplicationSignInManager<TUser> signInManager,
            IIdentityServerInteractionService interaction,
            IClientStore clientStore,
            IAuthenticationSchemeProvider schemeProvider,
            IEventService events,
            IEmailSender emailSender,
            IGenericControllerLocalizer<AccountController<TUser, TKey>> localizer,
            LoginConfiguration loginConfiguration,
            RegisterConfiguration registerConfiguration,
            IdentityOptions identityOptions,
            ILogger<AccountController<TUser, TKey>> logger,
            IConfiguration iConfig,
            IHttpClientFactory httpClientFactory
            )
        {
            _userResolver = userResolver;
            _userManager = userManager;
            _signInManager = signInManager;
            _interaction = interaction;
            _clientStore = clientStore;
            _schemeProvider = schemeProvider;
            _events = events;
            _emailSender = emailSender;
            _localizer = localizer;
            _loginConfiguration = loginConfiguration;
            _registerConfiguration = registerConfiguration;
            _identityOptions = identityOptions;
            _logger = logger;
            _configuration = iConfig;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Entry point into the login workflow
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Login(string returnUrl)
        {
            // build a model so we know what to show on the login page
            var vm = await BuildLoginViewModelAsync(returnUrl);

            if (vm.EnableLocalLogin == false && vm.ExternalProviders.Count() == 1)
            {
                // only one option for logging in
                return ExternalLogin(vm.ExternalProviders.First().AuthenticationScheme, returnUrl);
            }

            return View(vm);
        }

        /// <summary>
        /// Handle postback from username/password login
        /// </summary>
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginInputModel model, string button)
        {
            // check if we are in the context of an authorization request
            var context = await _interaction.GetAuthorizationContextAsync(model.ReturnUrl);

            // the user clicked the "cancel" button
            if (button != "login")
            {
                if (context != null)
                {
                    // if the user cancels, send a result back into IdentityServer as if they 
                    // denied the consent (even if this client does not require consent).
                    // this will send back an access denied OIDC error response to the client.
                    await _interaction.DenyAuthorizationAsync(context, AuthorizationError.AccessDenied);

                    // we can trust model.ReturnUrl since GetAuthorizationContextAsync returned non-null
                    if (context.IsNativeClient())
                    {
                        // The client is native, so this change in how to
                        // return the response is for better UX for the end user.
                        return this.LoadingPage("Redirect", model.ReturnUrl);
                    }

                    return Redirect(model.ReturnUrl);
                }

                // since we don't have a valid context, then we just go back to the home page
                return Redirect("~/");
            }

            if (ModelState.IsValid)
            {
                var user = await _userResolver.GetUserAsync(model.Username);

                if (user != default(TUser))
                {
                    var result = await _signInManager.PasswordSignInAsync(user.UserName, model.Password,
                        model.RememberLogin, lockoutOnFailure: true);
                    if (result.Succeeded)
                    {
                        await _events.RaiseAsync(new UserLoginSuccessEvent(user.UserName, user.Id.ToString(),
                            user.UserName));

                        if (context != null)
                        {
                            if (context.IsNativeClient())
                            {
                                // The client is native, so this change in how to
                                // return the response is for better UX for the end user.
                                return this.LoadingPage("Redirect", model.ReturnUrl);
                            }


                            // we can trust model.ReturnUrl since GetAuthorizationContextAsync returned non-null
                            return Redirect(model.ReturnUrl);
                        }

                        // request for a local page
                        if (Url.IsLocalUrl(model.ReturnUrl))
                        {
                            return Redirect(model.ReturnUrl);
                        }

                        if (string.IsNullOrEmpty(model.ReturnUrl))
                        {
                            return Redirect("~/");
                        }

                        // user might have clicked on a malicious link - should be logged
                        throw new Exception("invalid return URL");
                    }

                    if (result.RequiresTwoFactor)
                    {
                        return RedirectToAction(nameof(LoginWith2fa),
                            new { model.ReturnUrl, RememberMe = model.RememberLogin });
                    }

                    if (result.IsLockedOut)
                    {
                        return View("Lockout");
                    }
                }

                await _events.RaiseAsync(new UserLoginFailureEvent(model.Username, "invalid credentials",
                    clientId: context?.Client.ClientId));
                ModelState.AddModelError(string.Empty, AccountOptions.InvalidCredentialsErrorMessage);
            }

            // something went wrong, show form with error
            var vm = await BuildLoginViewModelAsync(model);
            return View(vm);
        }


        /// <summary>
        /// Show logout page
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Logout(string logoutId)
        {
            // build a model so the logout page knows what to display
            var vm = await BuildLogoutViewModelAsync(logoutId);

            if (vm.ShowLogoutPrompt == false)
            {
                // if the request for logout was properly authenticated from IdentityServer, then
                // we don't need to show the prompt and can just log the user out directly.
                return await Logout(vm);
            }

            return View(vm);
        }

        /// <summary>
        /// Handle logout page postback
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout(LogoutInputModel model)
        {
            // build a model so the logged out page knows what to display
            var vm = await BuildLoggedOutViewModelAsync(model.LogoutId);

            if (User?.Identity.IsAuthenticated == true)
            {
                // delete local authentication cookie
                await _signInManager.SignOutAsync();

                // raise the logout event
                await _events.RaiseAsync(new UserLogoutSuccessEvent(User.GetSubjectId(), User.GetDisplayName()));
            }

            // check if we need to trigger sign-out at an upstream identity provider
            if (vm.TriggerExternalSignout)
            {
                // build a return URL so the upstream provider will redirect back
                // to us after the user has logged out. this allows us to then
                // complete our single sign-out processing.
                string url = Url.Action("Logout", new { logoutId = vm.LogoutId });

                // this triggers a redirect to the external provider for sign-out
                return SignOut(new AuthenticationProperties { RedirectUri = url }, vm.ExternalAuthenticationScheme);
            }

            return View("LoggedOut", vm);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> ConfirmEmail(string userId, string code)
        {
            if (userId == null || code == null)
            {
                return View("Error");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return View("Error");
            }

            code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));

            var result = await _userManager.ConfirmEmailAsync(user, code);

            if (result.Succeeded == true)
            {
                //send welcome email
                string efApiBaseUrl =
                    _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl").Value;
                HttpClient client = new HttpClient();
                var message = new EventMessage()
                {
                    UserId = new Guid(user.Id.ToString()),
                    EventType = "User Account Created"
                };
                string messageUrl = $"{efApiBaseUrl}/workflow/message";
                var messageResponse = await client.PostAsJsonAsync(messageUrl, message);
            }

            string redirectUrl = _configuration.GetSection("Urls").GetSection("returnUrl").Value;

            return Redirect(redirectUrl);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ForgotPassword()
        {
            return View(new ForgotPasswordViewModel());
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                TUser user = null;
                switch (model.Policy)
                {
                    case LoginResolutionPolicy.Email:
                        try
                        {
                            user = await _userManager.FindByEmailAsync(model.Email);
                        }
                        catch (Exception ex)
                        {
                            // in case of multiple users with the same email this method would throw and reveal that the email is registered
                            _logger.LogError(
                                "Error retrieving user by email ({0}) for forgot password functionality: {1}",
                                model.Email, ex.Message);
                            user = null;
                        }

                        break;
                    case LoginResolutionPolicy.Username:
                        user = await _userManager.FindByNameAsync(model.Username);
                        break;
                }

                if (user == null)
                {
                    // Don't reveal that the user does not exist
                    return View("ForgotPasswordConfirmation");
                }

                var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var callbackUrl = Url.Action("ResetPassword", "Account", new { userId = user.Id, code },
                    HttpContext.Request.Scheme);

                if (user != null)
                {
                    //password reset

                    var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
                    string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
                    HttpClient client = new HttpClient();
                    var message = new EventMessage()
                    {
                        UserId = new Guid(user.Id.ToString()),
                        EventType = "Password Reset",
                        VerificationLink = callbackUrl,
                        MetaData = JsonSerializer.Serialize(new { toEmail = model.Email })
                    };
                    string messageUrl = $"{api}/workflow/message";
                    var messageResponse = await client.PostAsJsonAsync(messageUrl, message);
                }

                return View("ForgotPasswordConfirmation");
            }

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ResetPassword(string code = null)
        {
            return code == null ? View("Error") : View();
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetPassword(ResetPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                // Don't reveal that the user does not exist
                return RedirectToAction(nameof(ResetPasswordConfirmation), "Account");
            }

            var code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(model.Code));
            var result = await _userManager.ResetPasswordAsync(user, code, model.Password);

            if (result.Succeeded)
            {
                string efBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFBaseUrl").Value;
                return Redirect($"{efBaseUrl}/auth-callback/external");
            }

            AddErrors(result);

            return View();
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ResetPasswordConfirmation()
        {
            return View();
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ForgotPasswordConfirmation()
        {
            return View();
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Classlink(string code)
        {
            try
            {
                var idServerUrl = Request.Host.ToUriComponent();
                string clientid = "c16655105539316a66c3d6c63b66b46ff343a0c6251f";
                string classlinkClientSecret = "75d9d3f560bfdd693fb8f345c17bbbb1";

                //Get Token            
                var client = new HttpClient()
                {
                    BaseAddress = new Uri("https://launchpad.classlink.com")
                };

                string efBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFBaseUrl").Value;
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                    Convert.ToBase64String(
                        System.Text.ASCIIEncoding.ASCII.GetBytes($"{clientid}:{classlinkClientSecret}")));
                var data = new
                {
                    code = code,
                    grant_type = "authorization_code",
                    redirect_uri = $"{efBaseUrl}/rostering/classlink"
                    //redirect_uri = $"https://edgefactor.com/rostering/classlink"
                };

                var response = await client.PostAsJsonAsync<dynamic>("/oauth2/v2/token", data);
                response.EnsureSuccessStatusCode();
                var json = JsonDocument.Parse(await response.Content.ReadAsStringAsync());
                var accessToken = json.RootElement.GetString("access_token");


                //Get Claims
                var userInformationEndpoint = "https://nodeapi.classlink.com/v2/my/info";
                var request = new HttpRequestMessage(HttpMethod.Get, userInformationEndpoint);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var client1 = new HttpClient();
                var response1 = await client1.SendAsync(request);
                response1.EnsureSuccessStatusCode();
                var json1 = JsonDocument.Parse(await response1.Content.ReadAsStringAsync());
                var userInfo = json1.RootElement;


                //Get / Create ID User

                var user = new TUser
                {
                    //urn:clever:user_id
                    UserName = userInfo.GetString("Email") ??
                               userInfo.GetString("SourcedId") ?? userInfo.GetString("UserId"),
                    Email = userInfo.GetString("Email") ?? $"{userInfo.GetString("SourcedId")}@classlink.com"
                };

                user = await _userManager.FindByEmailAsync(user.Email);

                if (user == null || user.Id == null || string.IsNullOrEmpty(user.Id.ToString()))
                {
                    user = new TUser
                    {
                        UserName = userInfo.GetString("Email") ??
                                   userInfo.GetString("SourcedId") ?? userInfo.GetString("UserId"),
                        Email = userInfo.GetString("Email") ?? $"{userInfo.GetString("SourcedId")}@classlink.com"
                    };
                    await _userManager.CreateAsync(user);
                }

                var claims = await _userManager.GetClaimsAsync(user);
                if (!claims.Any())
                {
                    var userClaims = new List<Claim>
                    {
                        new("name", userInfo.GetString("DisplayName")),
                        new("given_name", userInfo.GetString("FirstName") ?? ""),
                        new("family_name", userInfo.GetString("LastName") ?? ""),
                        new("profile", userInfo.GetString("Profile") ?? "student"),
                        new("email", userInfo.GetString("Email") ?? "")
                    };
                    await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());
                }

                //Always refresh access token
                foreach (var claim in claims.Where(x => x.Type == Path.GetFileName(ClaimTypes.UserData)))
                {
                    await _userManager.RemoveClaimAsync(user, claim);
                }

                await _userManager.AddClaimAsync(user,
                    new Claim(Path.GetFileName(ClaimTypes.UserData), accessToken ?? ""));

                var logins = await _userManager.GetLoginsAsync(user);

                UserLoginInfo info = new UserLoginInfo("Classlink", userInfo.GetString("UserId"), "OAuth") { };

                await _userManager.AddLoginAsync(user, info);
                await _signInManager.SignInAsync(user, isPersistent: false);

                if (info.LoginProvider == "Classlink")
                {
                    string efApiBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl")
                        .Value;
                    string getUrl = $"{efApiBaseUrl}/rostering/classlink/sync/{user.Id}?clientid={clientid}";
                    var client2 = new HttpClient();
                    var response2 = await client2.GetAsync(getUrl);
                    response2.EnsureSuccessStatusCode();
                }

                return Redirect($"{efBaseUrl}/auth-callback/external");
            }
            catch (Exception ex)
            {
                var vm = new ErrorViewModel()
                {
                    Error = new ErrorMessage()
                    {
                        Error = ex.Message,
                        ErrorDescription = ex.InnerException?.Message
                    }
                };
                return View("Error", vm);
            }
        }


        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Clever(string code, string clientid)
        {
            try
            {
                var idServerUrl = Request.Host.ToUriComponent();
                string cleverClientSecret = string.Empty;
                switch (clientid)
                {
                    case "20f9594416ea443cecd6":
                        {
                            cleverClientSecret = "be963527adb81829c19148555299c0576be60370";
                            break;
                        }
                    case "002c5ae988f478b3c9a5":
                        {
                            cleverClientSecret = "9db396986414bbba046c3924683d739393f196d2";
                            break;
                        }
                    case "c778a7bb8ec7e93bee6f":
                        {
                            cleverClientSecret = "53ddd26946f41e51516423f1103c48fa91675256";
                            break;
                        }
                    default:
                        break;
                }

                //Get Token            
                var client = new HttpClient()
                {
                    BaseAddress = new Uri("https://clever.com")
                };


                //string cleverClientId = _configuration.GetSection("ExternalProvidersConfiguration").GetSection("CleverClientId").Value;
                //string cleverClientSecret = _configuration.GetSection("ExternalProvidersConfiguration").GetSection("CleverClientSecret").Value;

                string efBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFBaseUrl").Value;
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                    Convert.ToBase64String(
                        System.Text.ASCIIEncoding.ASCII.GetBytes($"{clientid}:{cleverClientSecret}")));
                var data = new
                {
                    code = code,
                    grant_type = "authorization_code",
                    // redirect_uri = $"https://{idServerUrl}/account/clever?clientid={clientid}"
                    // redirect_uri = $"https://edgefactor.com/rostering/clever?clientid={clientid}"
                    redirect_uri = $"{efBaseUrl}/rostering/clever?clientid={clientid}"
                };
                var response = await client.PostAsJsonAsync<dynamic>("/oauth/tokens", data);
                response.EnsureSuccessStatusCode();
                var json = JsonDocument.Parse(await response.Content.ReadAsStringAsync());
                var accessToken = json.RootElement.GetString("access_token");


                //Get Claims
                var userInformationEndpoint = "https://api.clever.com/userinfo";
                var request = new HttpRequestMessage(HttpMethod.Get, userInformationEndpoint);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var client1 = new HttpClient();
                var response1 = await client1.SendAsync(request);
                response1.EnsureSuccessStatusCode();
                var json1 = JsonDocument.Parse(await response1.Content.ReadAsStringAsync());
                var userInfo = json1.RootElement;


                //Get / Create ID User

                var user = new TUser
                {
                    //urn:clever:user_id
                    UserName = userInfo.GetString("email") ?? userInfo.GetString("user_id"),
                    Email = userInfo.GetString("email") ?? $"{userInfo.GetString("user_id")}@clever.com"
                };

                user = await _userManager.FindByEmailAsync(user.Email);

                if (user == null || user.Id == null || string.IsNullOrEmpty(user.Id.ToString()))
                {
                    user = new TUser
                    {
                        UserName = userInfo.GetString("email") ?? userInfo.GetString("user_id"),
                        Email = userInfo.GetString("email") ?? $"{userInfo.GetString("user_id")}@clever.com"
                    };
                    await _userManager.CreateAsync(user);
                }

                string efApiBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl").Value;
                var environment = _configuration.GetSection("Environment").GetChildren();
                var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
                string setupUser = $"{efApiBaseUrl}/user/{user.Id}/setuplocation";
                var userSetup = await client.PostAsJsonAsync(setupUser, locationId);
                userSetup.EnsureSuccessStatusCode();

                var claims = await _userManager.GetClaimsAsync(user);
                if (!claims.Any())
                {
                    var userClaims = new List<Claim>
                    {
                        new("name", userInfo.GetString("given_name") + " " + userInfo.GetString("family_name")),
                        new("given_name", userInfo.GetString("given_name") ?? ""),
                        new("family_name", userInfo.GetString("family_name") ?? ""),
                        new("profile", userInfo.GetString("user_type") ?? "student"),
                        new("email", userInfo.GetString("email") ?? ""),
                        new(Path.GetFileName(ClaimTypes.UserData), accessToken ?? "")
                    };
                    await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());
                }

                //Always refresh access token
                foreach (var claim in claims.Where(x => x.Type == Path.GetFileName(ClaimTypes.UserData)))
                {
                    await _userManager.RemoveClaimAsync(user, claim);
                }

                await _userManager.AddClaimAsync(user,
                    new Claim(Path.GetFileName(ClaimTypes.UserData), accessToken ?? ""));

                var logins = await _userManager.GetLoginsAsync(user);

                UserLoginInfo info = new UserLoginInfo("Clever", userInfo.GetString("user_id"), "OAuth") { };

                await _userManager.AddLoginAsync(user, info);
                await _signInManager.SignInAsync(user, isPersistent: false);

                if (info.LoginProvider == "Clever")
                {
                    string getUrl = $"{efApiBaseUrl}/rostering/clever/sync/{user.Id}?clientid={clientid}";
                    var client2 = new HttpClient();
                    var response2 = await client2.GetAsync(getUrl);
                    response2.EnsureSuccessStatusCode();
                }

                return Redirect($"{efBaseUrl}/auth-callback/external");
            }
            catch (Exception ex)
            {
                var vm = new ErrorViewModel()
                {
                    Error = new ErrorMessage()
                    {
                        Error = ex.Message,
                        ErrorDescription = ex.InnerException?.Message
                    }
                };
                return View("Error", vm);
            }
        }


        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> D2L(Dictionary<string, object> state, string code = null, string refurl = null)
        {
            try
            {
                var call = "1";
                var referer = refurl ?? Request.Headers.Referer.FirstOrDefault() ?? "partners.brightspace.com/";
                //var referer = "dsbn.elearningontario.ca";
                var idServerUrl = Request.Host.ToUriComponent();
                var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
                var efBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFBaseUrl").Value;
                string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
                string getUrl = $"{api}/rostering/d2l/sso/auth?tenant={referer}";
                var client = new HttpClient();
                var efSSODetail = await client.GetFromJsonAsync<D2LSSOAuth>(getUrl);


                //Get Token            
                var basicClient = new HttpClient() { };

                basicClient.DefaultRequestHeaders.Accept.Add(
                    new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));
                basicClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                    Convert.ToBase64String(
                        System.Text.ASCIIEncoding.UTF8.GetBytes(
                            $"{efSSODetail.ExternalAuthId}:{efSSODetail.ExternalAuthSecret}")));

                var dict = new Dictionary<string, string>
                {
                    { "code", code },
                    { "grant_type", "authorization_code" },
                    //{ "redirect_uri", $"https://{idServerUrl}/account/d2l" }
                    //{ "redirect_uri", $"{api}/rostering/d2l_lti.aspx" }
                    { "redirect_uri", $"https://edgefactor.com/rostering/d2l_lti.aspx" }
                };
                call = "2";
                var req = new HttpRequestMessage(HttpMethod.Post, "https://auth.brightspace.com/core/connect/token")
                { Content = new FormUrlEncodedContent(dict) };
                var res = await basicClient.SendAsync(req);
                var xxx = await res.Content.ReadAsStringAsync();
                var c = await res.Content.ReadFromJsonAsync<D2LSSOToken>();
                res.EnsureSuccessStatusCode();

                call = "3";
                ////Get & Create/Update User
                var bearerClient = new HttpClient() { };
                bearerClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                bearerClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue("Bearer", c.AccessToken);

                var userInformationEndpoint = $"https://{efSSODetail.ExternalUrl}/d2l/api/lp/1.28/users/whoami";
                var userReq = new HttpRequestMessage(HttpMethod.Get, userInformationEndpoint);
                var userRes = await bearerClient.SendAsync(userReq);
                var userInfo = new D2LSSOUserInfo();
                if (userRes.IsSuccessStatusCode)
                {
                    userInfo = await userRes.Content.ReadFromJsonAsync<D2LSSOUserInfo>();
                }

                Request.Cookies.TryGetValue("email", out var email);
                var profileInfo = new D2LSSOProfileInfo();
                if (string.IsNullOrEmpty(email))
                {
                    //call = "4";
                    var profileEndPoint = $"https://{efSSODetail.ExternalUrl}/d2l/api/lp/1.28/profile/myProfile";
                    var profileReg = new HttpRequestMessage(HttpMethod.Get, profileEndPoint);
                    var profileRes = await bearerClient.SendAsync(profileReg);
                    if (profileRes.IsSuccessStatusCode)
                    {
                        profileInfo = await profileRes.Content.ReadFromJsonAsync<D2LSSOProfileInfo>();
                    }
                }

                //Get / Create ID User

                var newUser = new TUser
                {
                    //urn:clever:user_id
                    UserName = userInfo.UniqueName,
                    Email = email ?? profileInfo.Email ?? $"{userInfo.UniqueName}@d2l.com"
                };

                var user = await _userManager.FindByEmailAsync(newUser.Email);

                if (user == null || user.Id == null || string.IsNullOrEmpty(user.Id.ToString()))
                {
                    await _userManager.CreateAsync(newUser);
                    user = await _userManager.FindByEmailAsync(newUser.Email);
                }

                var login = await _userManager.GetLoginsAsync(user);
                if (login == null)
                {
                    throw new Exception("Authentication Error");
                }

                if (!login.Any(x => x.ProviderKey == userInfo.ProfileIdentifier))
                {
                    var info = new UserLoginInfo("D2L", userInfo.ProfileIdentifier, "OAuth") { };
                    await _userManager.AddLoginAsync(user, info);
                }

                var environment = registerConfiguration.GetSection("Environment").GetChildren();
                var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
                string setupUser = $"{api}/user/{user.Id}/setuplocation";
                var userSetup = await client.PostAsJsonAsync(setupUser, locationId);
                userSetup.EnsureSuccessStatusCode();

                var claims = await _userManager.GetClaimsAsync(user);
                if (!claims.Any())
                {
                    var userClaims = new List<Claim>
                    {
                        new("name", userInfo.FirstName + " " + userInfo.LastName),
                        new("given_name", userInfo.FirstName ?? ""),
                        new("family_name", userInfo.LastName ?? ""),
                        new("profile", "student"),
                        new("email", email ?? ""),
                        new(Path.GetFileName(ClaimTypes.UserData), c.AccessToken ?? "")
                    };
                    await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());
                }

                //Always refresh access token
                foreach (var claim in claims.Where(x => x.Type == Path.GetFileName(ClaimTypes.UserData)))
                {
                    await _userManager.RemoveClaimAsync(user, claim);
                }

                await _userManager.AddClaimAsync(user,
                    new Claim(Path.GetFileName(ClaimTypes.UserData), c.AccessToken ?? ""));
                await _signInManager.SignInAsync(user, isPersistent: false);

                try
                {
                    //Do Rostering            
                    string rosterUrl = $"{api}/rostering/d2l/sync/{user.Id}?tenant={referer}";
                    var client2 = new HttpClient();
                    var response2 = await client2.GetAsync(rosterUrl);
                    response2.EnsureSuccessStatusCode();
                    return Redirect($"{efBaseUrl}/auth-callback/external");

                }
                catch
                {
                    return Redirect($"{efBaseUrl}/auth-callback/external");
                }
            }
            catch (Exception ex)
            {
                var vm = new ErrorViewModel()
                {
                    Error = new ErrorMessage()
                    {
                        Error = ex.Message,
                        ErrorDescription = ex.InnerException?.Message
                    }
                };


                return View("Error", vm);
            }
        }


        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> D2LLink([FromForm] D2LExternalProvider body)
        {
            try
            {
                string efApiBaseUrl =
                    _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl").Value;
                string getUrl = $"{efApiBaseUrl}/rostering/d2l/sso/auth?tenant={body.ext_d2l_tenantid}";
                var client = new HttpClient();
                var res = await client.GetAsync(getUrl);
                res.EnsureSuccessStatusCode();
                var efSSODetail = await res.Content.ReadFromJsonAsync<D2LSSOAuth>();
                if (!string.IsNullOrEmpty(body.lis_person_contact_email_primary))
                {
                    Response.Cookies.Append("email", body.lis_person_contact_email_primary);
                }

                return Redirect(
                    $"https://auth.brightspace.com/oauth2/auth?client_id={efSSODetail.ExternalAuthId}&scope=core:*:*&response_type=code");
            }
            catch (Exception ex)
            {
                var vm = new ErrorViewModel()
                {
                    Error = new ErrorMessage()
                    {
                        Error = ex.Message,
                        ErrorDescription = ex?.InnerException?.Message
                    }
                };
                return View("Error", vm);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> ExternalLoginCallback(string returnUrl = null, string remoteError = null)
        {
            var info = await _signInManager.GetExternalLoginInfoAsync();

            if (info == null)
            {
                //  info = new ExternalLoginInfo(User, "Microsoft", User.Claims.Where(x => x.Type == "http://schemas.microsoft.com/identity/claims/objectidentifier").FirstOrDefault().Value.ToString(), "Microsoft");
                return RedirectToAction(nameof(Login));
            }

            // Sign in the user with this external login provider if the user already has a login.
            var result =
                await _signInManager.ExternalLoginSignInAsync(info.LoginProvider, info.ProviderKey,
                    isPersistent: false);
            if (result.Succeeded)
            {
                if (info.LoginProvider == "Clever")
                {
                    await CleverRosteringFlow(info);
                }

                return RedirectToLocal(returnUrl);
            }
            else if (result.RequiresTwoFactor)
            {
                return RedirectToAction(nameof(LoginWith2fa), new { ReturnUrl = returnUrl });
            }
            else if (result.IsLockedOut)
            {
                return View("Lockout");
            }

            else
            {
                if (info.LoginProvider == "Clever")
                {
                    await CleverNewUserFlow(info);
                }
                else
                {
                    await SocialNewUserFlow(info);
                }

                return RedirectToLocal(returnUrl);
            }

            // If the user does not have an account, then ask the user to create an account.
            //ViewData["ReturnUrl"] = returnUrl;
            //ViewData["LoginProvider"] = info.LoginProvider;
            //var email = info.Principal.FindFirstValue(ClaimTypes.Email);
            ////var userName = info.Principal.Identity.Name;
            //var userName = email;
            //return View("ExternalLoginConfirmation", new ExternalLoginConfirmationViewModel { Email = email, UserName = userName });
        }

        private async Task SocialNewUserFlow(ExternalLoginInfo info)
        {
            var newUser = new TUser
            {
                UserName = info.Principal.FindFirstValue(ClaimTypes.Email),
                Email = info.Principal.FindFirstValue(ClaimTypes.Email)
            };

            var user = await _userManager.FindByEmailAsync(newUser.Email);
            if (user == null || user.Id == null || string.IsNullOrEmpty(user.Id.ToString()))
            {
                var result = await _userManager.CreateAsync(newUser);
                if (result.Succeeded)
                {
                    user = await _userManager.FindByEmailAsync(newUser.Email);
                    result = await _userManager.AddLoginAsync(user, info);
                    if (result.Succeeded)
                    {
                        var userClaims = new List<Claim>
                                         {
                                             new("name",
                                                 info.Principal.FindFirstValue(ClaimTypes.GivenName) + " " +
                                                 info.Principal.FindFirstValue(ClaimTypes.Surname) ?? ""),
                                             new("given_name", info.Principal.FindFirstValue(ClaimTypes.GivenName) ?? ""),
                                             new("family_name", info.Principal.FindFirstValue(ClaimTypes.Surname) ?? ""),
                                             new("email", info.Principal.FindFirstValue(ClaimTypes.Email) ?? "")
                                         };

                        var claimResult = await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());

                        string domain = user.Email.Split('@')[1];
                        var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
                        string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
                        string identitySource = registerConfiguration.GetSection("IdentitySource").Value;

                        string getUrl =
                            $"{api}/user/register/link/{user.Id}?identitySource={identitySource}&domain={domain}";
                        var client = new HttpClient();
                        var environment = registerConfiguration.GetSection("Environment").GetChildren();
                        var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
                        string setupUser = $"{api}/user/{user.Id}/setuplocation";
                        var userSetup = await client.PostAsJsonAsync(setupUser, locationId);
                        userSetup.EnsureSuccessStatusCode();
                        _ = await client.GetAsync(getUrl);
                    }
                }
            }

            await _signInManager.SignInAsync(user, isPersistent: false);
        }

        private async Task CleverNewUserFlow(ExternalLoginInfo info)
        {
            var newUser = new TUser
            {
                UserName = info.Principal.FindFirstValue(ClaimTypes.Email) ??
                           info.Principal.FindFirstValue("urn:clever:user_id"),
                Email = info.Principal.FindFirstValue(ClaimTypes.Email) ??
                        $"{info.Principal.FindFirstValue("urn:clever:user_id")}@clever.com"
            };
            var user = await _userManager.FindByEmailAsync(newUser.Email);
            if (user == null || user.Id == null || string.IsNullOrEmpty(user.Id.ToString()))
            {
                await _userManager.CreateAsync(newUser);
                user = await _userManager.FindByEmailAsync(newUser.Email);
            }

            var userClaims = new List<Claim>
            {
                new("name",
                    info.Principal.FindFirstValue(ClaimTypes.GivenName) + " " +
                    info.Principal.FindFirstValue(ClaimTypes.Surname) ?? ""),
                new("given_name", info.Principal.FindFirstValue(ClaimTypes.GivenName) ?? ""),
                new("family_name", info.Principal.FindFirstValue(ClaimTypes.Surname) ?? ""),
                new("profile", info.Principal.FindFirstValue("urn:clever:user_type") ?? "Student"),
                new("picture", ""),
                new("email", info.Principal.FindFirstValue(ClaimTypes.Email) ?? ""),
                new("phone_number", ""),
                new("address", info.Principal.FindFirstValue(ClaimTypes.StreetAddress) ?? ""),
                new("country", info.Principal.FindFirstValue(ClaimTypes.Country) ?? ""),
                new("stateorprovince", info.Principal.FindFirstValue(ClaimTypes.StateOrProvince) ?? ""),
                new("postalcode", info.Principal.FindFirstValue(ClaimTypes.PostalCode) ?? ""),
                new(Path.GetFileName(ClaimTypes.UserData),
                    info.AuthenticationTokens.Where(x => x.Name == "access_token").Select(x => x.Value)
                        .FirstOrDefault() ?? "")
            };

            var claimResult = await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());
            var logins = await _userManager.GetLoginsAsync(user);
            await _userManager.AddLoginAsync(user, info);
            await _signInManager.SignInAsync(user, isPersistent: false);

            var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
            string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
            string getUrl = $"{api}/rostering/clever/sync/{user.Id}";

            var client = new HttpClient();
            var response = await client.GetAsync(getUrl);
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        }

        private async Task CleverRosteringFlow(ExternalLoginInfo info)
        {
            var user = await _userManager.FindByEmailAsync(info.Principal.FindFirstValue(ClaimTypes.Email) ??
                                                           $"{info.Principal.FindFirstValue("urn:clever:user_id")}@clever.com");
            var claims = await _userManager.GetClaimsAsync(user);

            foreach (var claim in claims.Where(x => x.Type == Path.GetFileName(ClaimTypes.UserData)))
            {
                await _userManager.RemoveClaimAsync(user, claim);
            }

            await _userManager.AddClaimAsync(user,
                new Claim(Path.GetFileName(ClaimTypes.UserData),
                    info.AuthenticationTokens.Where(x => x.Name == "access_token").Select(x => x.Value)
                        .FirstOrDefault() ?? ""));

            var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
            string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
            string getUrl = $"{api}/rostering/clever/sync/{user.Id}";
            var client = new HttpClient();
            var environment = registerConfiguration.GetSection("Environment").GetChildren();
            var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
            string setupUser = $"{api}/user/{user.Id}/setuplocation";
            var response = await client.GetAsync(getUrl);
            var responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        }

        [HttpPost]
        [HttpGet]
        [AllowAnonymous]
        public IActionResult ExternalLogin(string provider, string returnUrl = null)
        {
            // Request a redirect to the external login provider.
            var redirectUrl = Url.Action("ExternalLoginCallback", "Account", new { ReturnUrl = returnUrl });
            var properties = _signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);

            return Challenge(properties, provider);
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExternalLoginConfirmation(ExternalLoginConfirmationViewModel model,
            string returnUrl = null)
        {
            returnUrl = returnUrl ?? Url.Content("~/");

            // Get the information about the user from the external login provider
            var info = await _signInManager.GetExternalLoginInfoAsync();
            if (info == null)
            {
                return View("ExternalLoginFailure");
            }

            if (ModelState.IsValid)
            {
                var user = new TUser
                {
                    UserName = model.Email,
                    Email = model.Email
                };

                var result = await _userManager.CreateAsync(user);
                if (result.Succeeded)
                {
                    user = await _userManager.FindByEmailAsync(user.Email);
                    result = await _userManager.AddLoginAsync(user, info);
                    if (result.Succeeded)
                    {
                        await _signInManager.SignInAsync(user, isPersistent: false);

                        return RedirectToLocal(returnUrl);
                    }
                }

                var client = new HttpClient();
                var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
                string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
                var environment = registerConfiguration.GetSection("Environment").GetChildren();
                var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
                string setupUser = $"{api}/user/{user.Id}/setuplocation";
                var userSetup = await client.PostAsJsonAsync(setupUser, locationId);

                AddErrors(result);
            }

            ViewData["LoginProvider"] = info.LoginProvider;
            ViewData["ReturnUrl"] = returnUrl;

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> LoginWithRecoveryCode(string returnUrl = null)
        {
            // Ensure the user has gone through the username & password screen first
            var user = await _signInManager.GetTwoFactorAuthenticationUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException(_localizer["Unable2FA"]);
            }

            var model = new LoginWithRecoveryCodeViewModel()
            {
                ReturnUrl = returnUrl
            };

            return View(model);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> LoginWithRecoveryCode(LoginWithRecoveryCodeViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var user = await _signInManager.GetTwoFactorAuthenticationUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException(_localizer["Unable2FA"]);
            }

            var recoveryCode = model.RecoveryCode.Replace(" ", string.Empty);

            var result = await _signInManager.TwoFactorRecoveryCodeSignInAsync(recoveryCode);

            if (result.Succeeded)
            {
                return LocalRedirect(string.IsNullOrEmpty(model.ReturnUrl) ? "~/" : model.ReturnUrl);
            }

            if (result.IsLockedOut)
            {
                return View("Lockout");
            }

            ModelState.AddModelError(string.Empty, _localizer["InvalidRecoveryCode"]);

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> LoginWith2fa(bool rememberMe, string returnUrl = null)
        {
            // Ensure the user has gone through the username & password screen first
            var user = await _signInManager.GetTwoFactorAuthenticationUserAsync();

            if (user == null)
            {
                throw new InvalidOperationException(_localizer["Unable2FA"]);
            }

            var model = new LoginWith2faViewModel()
            {
                ReturnUrl = returnUrl,
                RememberMe = rememberMe
            };

            return View(model);
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> LoginWith2fa(LoginWith2faViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var user = await _signInManager.GetTwoFactorAuthenticationUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException(_localizer["Unable2FA"]);
            }

            var authenticatorCode = model.TwoFactorCode.Replace(" ", string.Empty).Replace("-", string.Empty);

            var result =
                await _signInManager.TwoFactorAuthenticatorSignInAsync(authenticatorCode, model.RememberMe,
                    model.RememberMachine);

            if (result.Succeeded)
            {
                return LocalRedirect(string.IsNullOrEmpty(model.ReturnUrl) ? "~/" : model.ReturnUrl);
            }

            if (result.IsLockedOut)
            {
                return View("Lockout");
            }

            ModelState.AddModelError(string.Empty, _localizer["InvalidAuthenticatorCode"]);

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> RegisterAsync(string returnUrl = null)
        {
            if (!_registerConfiguration.Enabled)
            {
                return View("RegisterFailure");
            }

            var vm = await BuildRegisterViewModelAsync(returnUrl);

            if (vm.EnableLocalLogin == false && vm.ExternalProviders.Count() == 1)
            {
                // only one option for logging in
                return ExternalLogin(vm.ExternalProviders.First().AuthenticationScheme, returnUrl);
            }

            return View(vm);


            //ViewData["ReturnUrl"] = returnUrl;

            //return _loginConfiguration.ResolutionPolicy switch
            //{
            //    LoginResolutionPolicy.Username => View(),
            //    LoginResolutionPolicy.Email => View("RegisterWithoutUsername"),
            //    _ => View("RegisterFailure")
            //};
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterViewModel model, string returnUrl = null, bool IsCalledFromRegisterWithoutUsername = false)
        {
            if (!_registerConfiguration.Enabled)
            {
                return View("RegisterFailure");
            }

            returnUrl ??= Url.Content("~/");

            ViewData["ReturnUrl"] = returnUrl;

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            if (ModelState.IsValid)
            {
                //This will be moved to the html as a scriptvalidator.
                int monthNumber = DateTime.ParseExact(model.Month, "MMMM", CultureInfo.CurrentCulture).Month;

                var dateStamp = new DateTime(Convert.ToInt32(model.Year), monthNumber, 1);
                var age =
                    (int.Parse(DateTime.UtcNow.ToString("yyyyMMdd")) - int.Parse(dateStamp.ToString("yyyyMMdd"))) /
                    10000;


                //compare age to that of the age in the country
                if (age <= 13)
                {
                    ModelState.AddModelError(string.Empty,
                        "You are using an ineligible age to register on Edge Factor.");
                }
            }

            var user = new TUser
            {
                UserName = model.Email,
                Email = model.Email
            };

            var result = await _userManager.CreateAsync(user, model.Password);
            if (!result.Succeeded)
            {
                AddErrors(result);
                return View(model);
            }

            user = await _userManager.FindByEmailAsync(user.Email);
            var client = new HttpClient();

            await Task.Delay(1000);
            var userClaims = new List<Claim>
            {
                new("email", model.Email),
                new("given_name", model.FirstName),
                new("family_name", model.LastName),
                new("name", model.FirstName + " " + model.LastName),
                new("month", model.Month),
                new("year", model.Year),
                new("country", model.Country),
                new("postal_code", model.PostalCode),
                new("profile", ""),
                new("picture", ""),
                new("phone_number", ""),
                new("address", ""),
                new("province", "")
            };

            var claimResult = await _userManager.AddClaimsAsync(user, userClaims.AsEnumerable());

            if (result.Succeeded && claimResult.Succeeded)
            {
                var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
                string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
                var environment = registerConfiguration.GetSection("Environment").GetChildren();
                var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
                string setupUser = $"{api}/user/{user.Id}/setuplocation";
                var userSetup = await client.PostAsJsonAsync(setupUser, locationId);
                Uri returnUri = new Uri(api + returnUrl);
                string joinCode = HttpUtility.ParseQueryString(returnUri.Query).Get("joinCode");

                if (!string.IsNullOrWhiteSpace(joinCode))
                {
                    var joinCodeUrl = $"{api}/user/joincampaign/{joinCode}?userId={user.Id}";
                    var joinCodeResponse = await client.GetAsync(joinCodeUrl);
                }

                string domain = user.Email.Split('@')[1];
                await LinkUser(new Guid(user.Id.ToString()), domain);

                var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var callbackUrl = Url.Action("ConfirmEmail", "Account", new { userId = user.Id, code },
                    HttpContext.Request.Scheme);

                var message = new EventMessage()
                {
                    UserId = new Guid(user.Id.ToString()),
                    EventType = "Verify Email Address",
                    VerificationLink = callbackUrl
                };

                string messageUrl = $"{api}/workflow/message";
                var messageResponse = await client.PostAsJsonAsync(messageUrl, message);

                if (_identityOptions.SignIn.RequireConfirmedAccount)
                {
                    return View("RegisterConfirmation");
                }
                else
                {
                    await _signInManager.SignInAsync(user, isPersistent: false);
                    return LocalRedirect(returnUrl);
                }
            }
            else
            {
                AddErrors(result);
            }

            // If we got this far, something failed, redisplay form
            if (IsCalledFromRegisterWithoutUsername)
            {
                var registerWithoutUsernameModel = new RegisterWithoutUsernameViewModel
                {
                    Email = model.Email,
                    Password = model.Password
                };

                return View("RegisterWithoutUsername", registerWithoutUsernameModel);
            }
            else
            {
                return View(model);
            }
        }

        private async Task LinkUser(Guid userId, string domain)
        {
            string identitySource =
                _configuration.GetSection("RegisterConfiguration").GetSection("IdentitySource").Value;
            string efApiBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl").Value;

            var client = new HttpClient();
            string getUrl =
                $"{efApiBaseUrl}/user/register/link/{userId}?identitySource={identitySource}&domain={domain}";
            var response = await client.GetAsync(getUrl);
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        }


        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RegisterWithoutUsername(RegisterWithoutUsernameViewModel model, string returnUrl = null)
        {
            var registerModel = new RegisterViewModel
            {
                UserName = model.Email,
                Email = model.Email,
                Password = model.Password
            };

            return await Register(registerModel, returnUrl, true);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<JsonResult> CheckEmailExists(string email)
        {
            string efApiBaseUrl = _configuration.GetSection("RegisterConfiguration").GetSection("EFApiBaseUrl").Value;
            string getUrl = $"{efApiBaseUrl}/user/lookup/email?email={email}";
            var client = new HttpClient();
            var res = await client.GetAsync(getUrl);
            res.EnsureSuccessStatusCode();

            var emailExists = await res.Content.ReadFromJsonAsync<bool>();
            if (emailExists)
            {
                return Json(false);
            }

            return Json(true);
        }


        /*****************************************/
        /* helper APIs for the AccountController */
        /*****************************************/
        private IActionResult RedirectToLocal(string returnUrl)
        {
            if (Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }

            return RedirectToAction(nameof(HomeController.Index), "Home");
        }

        private void AddErrors(IdentityResult result)
        {
            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }
        }

        private async Task<LoginViewModel> BuildLoginViewModelAsync(string returnUrl)
        {
            var context = await _interaction.GetAuthorizationContextAsync(returnUrl);
            if (context?.IdP != null && await _schemeProvider.GetSchemeAsync(context.IdP) != null)
            {
                var local = context.IdP == IdentityServerConstants.LocalIdentityProvider;

                // this is meant to short circuit the UI and only trigger the one external IdP
                var vm = new LoginViewModel
                {
                    EnableLocalLogin = local,
                    ReturnUrl = returnUrl,
                    Username = context?.LoginHint,
                };

                if (!local)
                {
                    vm.ExternalProviders = new[] { new ExternalProvider { AuthenticationScheme = context.IdP } };
                }

                return vm;
            }

            var schemes = await _schemeProvider.GetAllSchemesAsync();

            var providers = schemes
                .Where(x => x.DisplayName != null)
                .Select(x => new ExternalProvider
                {
                    DisplayName = x.Name,
                    AuthenticationScheme = x.Name,
                    LogoUri = x.Name
                }).ToList();

            var allowLocal = true;
            if (context?.Client.ClientId != null)
            {
                var client = await _clientStore.FindEnabledClientByIdAsync(context.Client.ClientId);
                if (client != null)
                {
                    allowLocal = client.EnableLocalLogin;

                    if (client.IdentityProviderRestrictions != null && client.IdentityProviderRestrictions.Any())
                    {
                        providers = providers.Where(provider =>
                            client.IdentityProviderRestrictions.Contains(provider.AuthenticationScheme)).ToList();
                    }
                }
            }

            return new LoginViewModel
            {
                AllowRememberLogin = AccountOptions.AllowRememberLogin,
                EnableLocalLogin = allowLocal && AccountOptions.AllowLocalLogin,
                ReturnUrl = returnUrl,
                Username = context?.LoginHint,
                ExternalProviders = providers.ToArray()
            };
        }

        private async Task<RegisterViewModel> BuildRegisterViewModelAsync(string returnUrl)
        {
            var context = await _interaction.GetAuthorizationContextAsync(returnUrl);

            var schemes = await _schemeProvider.GetAllSchemesAsync();

            var providers = schemes
                .Where(x => x.DisplayName != null)
                .Select(x => new ExternalProvider
                {
                    DisplayName = x.Name,
                    AuthenticationScheme = x.Name,
                    LogoUri = x.Name
                }).ToList();

            var allowLocal = true;

            return new RegisterViewModel
            {
                AllowRememberLogin = AccountOptions.AllowRememberLogin,
                EnableLocalLogin = allowLocal && AccountOptions.AllowLocalLogin,
                //Username = context?.LoginHint,
                ExternalProviders = providers.ToArray(),
                ReturnUrl = returnUrl
            };
        }

        private async Task<LoginViewModel> BuildLoginViewModelAsync(LoginInputModel model)
        {
            var vm = await BuildLoginViewModelAsync(model.ReturnUrl);
            vm.Username = model.Username;
            vm.RememberLogin = model.RememberLogin;
            return vm;
        }

        private async Task<LogoutViewModel> BuildLogoutViewModelAsync(string logoutId)
        {
            var vm = new LogoutViewModel { LogoutId = logoutId, ShowLogoutPrompt = AccountOptions.ShowLogoutPrompt };

            if (User?.Identity.IsAuthenticated != true)
            {
                // if the user is not authenticated, then just show logged out page
                vm.ShowLogoutPrompt = false;
                return vm;
            }

            var context = await _interaction.GetLogoutContextAsync(logoutId);
            if (context?.ShowSignoutPrompt == false)
            {
                // it's safe to automatically sign-out
                vm.ShowLogoutPrompt = false;
                return vm;
            }

            // show the logout prompt. this prevents attacks where the user
            // is automatically signed out by another malicious web page.
            return vm;
        }

        private async Task<LoggedOutViewModel> BuildLoggedOutViewModelAsync(string logoutId)
        {
            // get context information (client name, post logout redirect URI and iframe for federated signout)
            var logout = await _interaction.GetLogoutContextAsync(logoutId);

            var vm = new LoggedOutViewModel
            {
                AutomaticRedirectAfterSignOut = AccountOptions.AutomaticRedirectAfterSignOut,
                PostLogoutRedirectUri = logout?.PostLogoutRedirectUri,
                ClientName = string.IsNullOrEmpty(logout?.ClientName) ? logout?.ClientId : logout?.ClientName,
                SignOutIframeUrl = logout?.SignOutIFrameUrl,
                LogoutId = logoutId
            };

            if (User?.Identity.IsAuthenticated == true)
            {
                var idp = User.FindFirst(JwtClaimTypes.IdentityProvider)?.Value;
                if (idp != null && idp != IdentityServerConstants.LocalIdentityProvider)
                {
                    var providerSupportsSignout = await HttpContext.GetSchemeSupportsSignOutAsync(idp);
                    if (providerSupportsSignout)
                    {
                        if (vm.LogoutId == null)
                        {
                            // if there's no current logout context, we need to create one
                            // this captures necessary info from the current logged in user
                            // before we signout and redirect away to the external IdP for signout
                            vm.LogoutId = await _interaction.CreateLogoutContextAsync();
                        }

                        vm.ExternalAuthenticationScheme = idp;
                    }
                }
            }

            return vm;
        }
    }
}