﻿using EdgeFactor.ID.STS.Identity.Configuration;
using EdgeFactor.ID.STS.Identity.Configuration.ApplicationParts;
using EdgeFactor.ID.STS.Identity.Configuration.Constants;
using EdgeFactor.ID.STS.Identity.Configuration.Interfaces;
using EdgeFactor.ID.STS.Identity.Helpers.Localization;
using IdentityServer4.EntityFramework.Storage;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OAuth;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Skoruba.IdentityServer4.Admin.EntityFramework.Configuration.Configuration;
using Skoruba.IdentityServer4.Admin.EntityFramework.Configuration.MySql;
using Skoruba.IdentityServer4.Admin.EntityFramework.Configuration.PostgreSQL;
using Skoruba.IdentityServer4.Admin.EntityFramework.Configuration.SqlServer;
using Skoruba.IdentityServer4.Admin.EntityFramework.Helpers;
using Skoruba.IdentityServer4.Admin.EntityFramework.Interfaces;
using Skoruba.IdentityServer4.Shared.Configuration.Authentication;
using Skoruba.IdentityServer4.Shared.Configuration.Configuration.Identity;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text.Json;
using System.Threading.Tasks;

namespace EdgeFactor.ID.STS.Identity.Helpers
{
    public static class StartupHelpers
    {
        /// <summary>
        /// Register services for MVC and localization including available languages
        /// </summary>
        /// <param name="services"></param>
        public static IMvcBuilder AddMvcWithLocalization<TUser, TKey>(this IServiceCollection services, IConfiguration configuration)
            where TUser : IdentityUser<TKey>
            where TKey : IEquatable<TKey>
        {
            services.AddLocalization(opts => { opts.ResourcesPath = ConfigurationConsts.ResourcesPath; });

            services.TryAddTransient(typeof(IGenericControllerLocalizer<>), typeof(GenericControllerLocalizer<>));

            var mvcBuilder = services.AddControllersWithViews(o =>
                {
                    o.Conventions.Add(new GenericControllerRouteConvention());
                })
                .AddViewLocalization(
                    LanguageViewLocationExpanderFormat.Suffix,
                    opts => { opts.ResourcesPath = ConfigurationConsts.ResourcesPath; })
                .AddDataAnnotationsLocalization()
                .ConfigureApplicationPartManager(m =>
                {
                    m.FeatureProviders.Add(new GenericTypeControllerFeatureProvider<TUser, TKey>());
                });

            var cultureConfiguration = configuration.GetSection(nameof(CultureConfiguration)).Get<CultureConfiguration>();
            services.Configure<RequestLocalizationOptions>(
                opts =>
                {
                    // If cultures are specified in the configuration, use them (making sure they are among the available cultures),
                    // otherwise use all the available cultures
                    var supportedCultureCodes = (cultureConfiguration?.Cultures?.Count > 0 ?
                        cultureConfiguration.Cultures.Intersect(CultureConfiguration.AvailableCultures) :
                        CultureConfiguration.AvailableCultures).ToArray();

                    if (!supportedCultureCodes.Any())
                    {
                        supportedCultureCodes = CultureConfiguration.AvailableCultures;
                    }

                    var supportedCultures = supportedCultureCodes.Select(c => new CultureInfo(c)).ToList();

                    // If the default culture is specified use it, otherwise use CultureConfiguration.DefaultRequestCulture ("en")
                    var defaultCultureCode = string.IsNullOrEmpty(cultureConfiguration?.DefaultCulture) ?
                        CultureConfiguration.DefaultRequestCulture : cultureConfiguration?.DefaultCulture;

                    // If the default culture is not among the supported cultures, use the first supported culture as default
                    if (!supportedCultureCodes.Contains(defaultCultureCode))
                    {
                        defaultCultureCode = supportedCultureCodes.FirstOrDefault();
                    }

                    opts.DefaultRequestCulture = new RequestCulture(defaultCultureCode);
                    opts.SupportedCultures = supportedCultures;
                    opts.SupportedUICultures = supportedCultures;
                });

            return mvcBuilder;
        }

        /// <summary>
        /// Using of Forwarded Headers and Referrer Policy
        /// </summary>
        /// <param name="app"></param>
        /// <param name="configuration"></param>
        public static void UseSecurityHeaders(this IApplicationBuilder app, IConfiguration configuration)
        {
            var forwardingOptions = new ForwardedHeadersOptions()
            {
                ForwardedHeaders = ForwardedHeaders.All
            };

            forwardingOptions.KnownNetworks.Clear();
            forwardingOptions.KnownProxies.Clear();

            app.UseForwardedHeaders(forwardingOptions);

            app.UseReferrerPolicy(options => options.NoReferrer());

            // CSP Configuration to be able to use external resources
            var cspTrustedDomains = new List<string>();
            configuration.GetSection(ConfigurationConsts.CspTrustedDomainsKey).Bind(cspTrustedDomains);
            if (cspTrustedDomains.Any())
            {
                app.UseCsp(csp =>
                {
                    csp.ImageSources(options =>
                    {
                        options.SelfSrc = true;
                        options.CustomSources = cspTrustedDomains;
                        options.Enabled = true;
                    });
                    csp.FontSources(options =>
                    {
                        options.SelfSrc = true;
                        options.CustomSources = cspTrustedDomains;
                        options.Enabled = true;
                    });
                    csp.ScriptSources(options =>
                    {
                        options.SelfSrc = true;
                        options.CustomSources = cspTrustedDomains;
                        options.Enabled = true;
                        options.UnsafeInlineSrc = true;
                    });
                    csp.StyleSources(options =>
                    {
                        options.SelfSrc = true;
                        options.CustomSources = cspTrustedDomains;
                        options.Enabled = true;
                        options.UnsafeInlineSrc = true;
                    });
                    csp.DefaultSources(options =>
                    {
                        options.SelfSrc = true;
                        options.CustomSources = cspTrustedDomains;
                        options.Enabled = true;
                    });
                });
            }

        }

        /// <summary>
        /// Register DbContexts for IdentityServer ConfigurationStore, PersistedGrants, Identity and DataProtection
        /// Configure the connection strings in AppSettings.json
        /// </summary>
        /// <typeparam name="TConfigurationDbContext"></typeparam>
        /// <typeparam name="TPersistedGrantDbContext"></typeparam>
        /// <typeparam name="TIdentityDbContext"></typeparam>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void RegisterDbContexts<TIdentityDbContext, TConfigurationDbContext, TPersistedGrantDbContext, TDataProtectionDbContext>(this IServiceCollection services, IConfiguration configuration)
            where TIdentityDbContext : DbContext
            where TPersistedGrantDbContext : DbContext, IAdminPersistedGrantDbContext
            where TConfigurationDbContext : DbContext, IAdminConfigurationDbContext
            where TDataProtectionDbContext : DbContext, IDataProtectionKeyContext
        {
            var databaseProvider = configuration.GetSection(nameof(DatabaseProviderConfiguration)).Get<DatabaseProviderConfiguration>();

            var identityConnectionString = configuration.GetConnectionString(ConfigurationConsts.IdentityDbConnectionStringKey);
            var configurationConnectionString = configuration.GetConnectionString(ConfigurationConsts.ConfigurationDbConnectionStringKey);
            var persistedGrantsConnectionString = configuration.GetConnectionString(ConfigurationConsts.PersistedGrantDbConnectionStringKey);
            var dataProtectionConnectionString = configuration.GetConnectionString(ConfigurationConsts.DataProtectionDbConnectionStringKey);

            switch (databaseProvider.ProviderType)
            {
                case DatabaseProviderType.SqlServer:
                    services.RegisterSqlServerDbContexts<TIdentityDbContext, TConfigurationDbContext, TPersistedGrantDbContext, TDataProtectionDbContext>(identityConnectionString, configurationConnectionString, persistedGrantsConnectionString, dataProtectionConnectionString);
                    break;
                case DatabaseProviderType.PostgreSQL:
                    services.RegisterNpgSqlDbContexts<TIdentityDbContext, TConfigurationDbContext, TPersistedGrantDbContext, TDataProtectionDbContext>(identityConnectionString, configurationConnectionString, persistedGrantsConnectionString, dataProtectionConnectionString);
                    break;
                case DatabaseProviderType.MySql:
                    services.RegisterMySqlDbContexts<TIdentityDbContext, TConfigurationDbContext, TPersistedGrantDbContext, TDataProtectionDbContext>(identityConnectionString, configurationConnectionString, persistedGrantsConnectionString, dataProtectionConnectionString);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(databaseProvider.ProviderType), $@"The value needs to be one of {string.Join(", ", Enum.GetNames(typeof(DatabaseProviderType)))}.");
            }
        }

        /// <summary>
        /// Register InMemory DbContexts for IdentityServer ConfigurationStore, PersistedGrants, Identity and DataProtection
        /// Configure the connection strings in AppSettings.json
        /// </summary>
        /// <typeparam name="TConfigurationDbContext"></typeparam>
        /// <typeparam name="TPersistedGrantDbContext"></typeparam>
        /// <typeparam name="TIdentityDbContext"></typeparam>
        /// <param name="services"></param>
        public static void RegisterDbContextsStaging<TIdentityDbContext, TConfigurationDbContext, TPersistedGrantDbContext, TDataProtectionDbContext>(
            this IServiceCollection services)
            where TIdentityDbContext : DbContext
            where TPersistedGrantDbContext : DbContext, IAdminPersistedGrantDbContext
            where TConfigurationDbContext : DbContext, IAdminConfigurationDbContext
            where TDataProtectionDbContext : DbContext, IDataProtectionKeyContext
        {
            var identityDatabaseName = Guid.NewGuid().ToString();
            services.AddDbContext<TIdentityDbContext>(optionsBuilder => optionsBuilder.UseInMemoryDatabase(identityDatabaseName));

            var configurationDatabaseName = Guid.NewGuid().ToString();
            var operationalDatabaseName = Guid.NewGuid().ToString();
            var dataProtectionDatabaseName = Guid.NewGuid().ToString();

            services.AddConfigurationDbContext<TConfigurationDbContext>(options =>
            {
                options.ConfigureDbContext = b => b.UseInMemoryDatabase(configurationDatabaseName);
            });

            services.AddOperationalDbContext<TPersistedGrantDbContext>(options =>
            {
                options.ConfigureDbContext = b => b.UseInMemoryDatabase(operationalDatabaseName);
            });

            services.AddDbContext<TDataProtectionDbContext>(options =>
            {
                options.UseInMemoryDatabase(dataProtectionDatabaseName);
            });
        }

        /// <summary>
        /// Add services for authentication, including Identity model, IdentityServer4 and external providers
        /// </summary>
        /// <typeparam name="TIdentityDbContext">DbContext for Identity</typeparam>
        /// <typeparam name="TUserIdentity">User Identity class</typeparam>
        /// <typeparam name="TUserIdentityRole">User Identity Role class</typeparam>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddAuthenticationServices<TIdentityDbContext, TUserIdentity, TUserIdentityRole>(this IServiceCollection services, IConfiguration configuration) where TIdentityDbContext : DbContext
            where TUserIdentity : class
            where TUserIdentityRole : class
        {
            var loginConfiguration = GetLoginConfiguration(configuration);
            var registrationConfiguration = GetRegistrationConfiguration(configuration);
            var identityOptions = configuration.GetSection(nameof(IdentityOptions)).Get<IdentityOptions>();

            services
                .AddSingleton(registrationConfiguration)
                .AddSingleton(loginConfiguration)
                .AddSingleton(identityOptions)
                .AddScoped<ApplicationSignInManager<TUserIdentity>>()
                .AddScoped<UserResolver<TUserIdentity>>()
                .AddScoped<IPasswordHasher<TUserIdentity>, CustomPasswordHasher<TUserIdentity>>()
                .AddIdentity<TUserIdentity, TUserIdentityRole>(options => configuration.GetSection(nameof(IdentityOptions)).Bind(options))
                .AddEntityFrameworkStores<TIdentityDbContext>()
                .AddDefaultTokenProviders();

            services.Configure<CookiePolicyOptions>(options =>
            {
                options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                options.Secure = CookieSecurePolicy.SameAsRequest;
                options.OnAppendCookie = cookieContext =>
                    AuthenticationHelpers.CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
                options.OnDeleteCookie = cookieContext =>
                    AuthenticationHelpers.CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
            });

            services.Configure<IISOptions>(iis =>
            {
                iis.AuthenticationDisplayName = "Windows";
                iis.AutomaticAuthentication = false;
            });

            var authenticationBuilder = services.AddAuthentication(options =>
            {
                options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
            }
            );

            AddExternalProviders(authenticationBuilder, configuration);
        }

        /// <summary>
        /// Get configuration for login
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static LoginConfiguration GetLoginConfiguration(IConfiguration configuration)
        {
            var loginConfiguration = configuration.GetSection(nameof(LoginConfiguration)).Get<LoginConfiguration>();

            // Cannot load configuration - use default configuration values
            if (loginConfiguration == null)
            {
                return new LoginConfiguration();
            }

            return loginConfiguration;
        }

        /// <summary>
        /// Get configuration for registration
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        private static RegisterConfiguration GetRegistrationConfiguration(IConfiguration configuration)
        {
            var registerConfiguration = configuration.GetSection(nameof(RegisterConfiguration)).Get<RegisterConfiguration>();

            // Cannot load configuration - use default configuration values
            if (registerConfiguration == null)
            {
                return new RegisterConfiguration();
            }

            return registerConfiguration;
        }

        /// <summary>
        /// Add configuration for IdentityServer4
        /// </summary>
        /// <typeparam name="TUserIdentity"></typeparam>
        /// <typeparam name="TConfigurationDbContext"></typeparam>
        /// <typeparam name="TPersistedGrantDbContext"></typeparam>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static IIdentityServerBuilder AddIdentityServer<TConfigurationDbContext, TPersistedGrantDbContext, TUserIdentity>(
            this IServiceCollection services,
            IConfiguration configuration)
            where TPersistedGrantDbContext : DbContext, IAdminPersistedGrantDbContext
            where TConfigurationDbContext : DbContext, IAdminConfigurationDbContext
            where TUserIdentity : class
        {
            var advancedConfiguration = configuration.GetSection(nameof(AdvancedConfiguration)).Get<AdvancedConfiguration>();

            var builder = services.AddIdentityServer(options =>
                {
                    options.Events.RaiseErrorEvents = true;
                    options.Events.RaiseInformationEvents = true;
                    options.Events.RaiseFailureEvents = true;
                    options.Events.RaiseSuccessEvents = true;
                    options.Authentication.CookieSlidingExpiration = true;

                    if (!string.IsNullOrEmpty(advancedConfiguration.IssuerUri))
                    {
                        options.IssuerUri = advancedConfiguration.IssuerUri;
                    }
                })
                .AddConfigurationStore<TConfigurationDbContext>()
                .AddOperationalStore<TPersistedGrantDbContext>()
                .AddAspNetIdentity<TUserIdentity>();
            //.AddLtiJwtBearerClientAuthentication()
            //.AddImpersonationSupport();


            builder.AddCustomSigningCredential(configuration);
            builder.AddCustomValidationKey(configuration);
            builder.AddExtensionGrantValidator<DelegationGrantValidator>();

            return builder;
        }

        /// <summary>
        /// Add external providers
        /// </summary>
        /// <param name="authenticationBuilder"></param>
        /// <param name="configuration"></param>
        private static void AddExternalProviders(AuthenticationBuilder authenticationBuilder, IConfiguration configuration)
        {
            var externalProviderConfiguration = configuration.GetSection(nameof(ExternalProvidersConfiguration)).Get<ExternalProvidersConfiguration>();

            if (externalProviderConfiguration.UseGoogleProvider)
            {
                authenticationBuilder.AddGoogle(options =>
                {
                    options.ClientId = externalProviderConfiguration.GoogleClientId;
                    options.ClientSecret = externalProviderConfiguration.GoogleClientSecret;
                });
            }

            if (externalProviderConfiguration.UseFacebookProvider)
            {
                authenticationBuilder.AddFacebook(options =>
                {
                    options.ClientId = externalProviderConfiguration.FacebookClientId;
                    options.ClientSecret = externalProviderConfiguration.FacebookClientSecret;
                });
            }

            if (externalProviderConfiguration.UseMicrosoftProvider)
            {
                authenticationBuilder.AddMicrosoftAccount(options =>
                {
                    options.ClientId = externalProviderConfiguration.MicrosoftClientId;
                    options.ClientSecret = externalProviderConfiguration.MicrosoftClientSecret;
                });
            }

            if (externalProviderConfiguration.UseCanvasProvider)
            {
                authenticationBuilder.AddOAuth("Canvas", options =>
                 {
                     options.ClientId = externalProviderConfiguration.CanvasClientId;
                     options.ClientSecret = externalProviderConfiguration.CanvasClientSecret;
                     options.CallbackPath = externalProviderConfiguration.CanvasCallbackPath;
                     options.AuthorizationEndpoint = "https://canvas.com/oauth";
                     options.TokenEndpoint = "https://canvas.com/oauth/token";

                 });
            }

            if (externalProviderConfiguration.UseCleverProvider)
            {
                authenticationBuilder.AddCookie().AddOAuth("Clever", options =>
                {
                    options.ClientId = externalProviderConfiguration.CleverClientId;
                    options.ClientSecret = externalProviderConfiguration.CleverClientSecret;
                    options.CallbackPath = externalProviderConfiguration.CleverCallbackPath;
                    options.AuthorizationEndpoint = "https://clever.com/oauth/authorize";
                    options.TokenEndpoint = "https://clever.com/oauth/tokens";
                    options.UserInformationEndpoint = "https://api.clever.com/userinfo";
                    options.SaveTokens = true;
                    options.Scope.Add("openid");
                    options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
                    options.ClaimActions.MapJsonKey(ClaimTypes.GivenName, "given_name");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Surname, "family_name");
                    options.ClaimActions.MapJsonKey("urn:clever:section", "sections");
                    options.ClaimActions.MapJsonKey("urn:clever:user_id", "multi_role_user_id");
                    options.ClaimActions.MapJsonKey("urn:clever:user_type", "user_type");
                    options.Events = new OAuthEvents
                    {
                        OnTicketReceived = async context =>
                        {
                            if (!context.ReturnUri.Contains('?'))
                            {
                                // context.HttpContext.User = context.Principal;
                                context.ReturnUri += $"/{context.Request.QueryString}";
                            }
                        },
                        OnCreatingTicket = async context =>
                        {
                            var request = new HttpRequestMessage(HttpMethod.Get, context.Options.UserInformationEndpoint);
                            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", context.AccessToken);
                            var response = await context.Backchannel.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, context.HttpContext.RequestAborted);
                            response.EnsureSuccessStatusCode();
                            var json = JsonDocument.Parse(await response.Content.ReadAsStringAsync());
                            var x = json.RootElement;
                            context.RunClaimActions(json.RootElement);
                        }
                    };
                });
            }

            if (externalProviderConfiguration.UseClassLinkProvider)
            {
                authenticationBuilder.AddOAuth("ClassLink", options =>
                {
                    options.ClientId = externalProviderConfiguration.ClassLinkClientId;
                    options.ClientSecret = externalProviderConfiguration.ClassLinkClientSecret;
                    options.CallbackPath = externalProviderConfiguration.ClassLinkCallbackPath;
                    options.AuthorizationEndpoint = "https://launchpad.classlink.com/oauth2/v2/auth";
                    options.TokenEndpoint = "https://launchpad.classlink.com/oauth2/v2/token";
                    options.UserInformationEndpoint = "https://nodeapi.classlink.com/v2/my/info";
                    options.SaveTokens = true;
                    options.Scope.Add("openid");
                    options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
                    options.ClaimActions.MapJsonKey(ClaimTypes.GivenName, "given_name");
                    options.ClaimActions.MapJsonKey(ClaimTypes.Surname, "family_name");
                    //options.ClaimActions.MapJsonKey("urn:clever:section", "sections");
                    //options.ClaimActions.MapJsonKey("urn:clever:user_id", "multi_role_user_id");
                    //options.ClaimActions.MapJsonKey("urn:clever:user_type", "user_type");
                    options.Events = new OAuthEvents
                    {

                        OnTicketReceived = async context =>
                        {
                            if (!context.ReturnUri.Contains('?'))
                            {
                                // context.HttpContext.User = context.Principal;
                                context.ReturnUri += $"/{context.Request.QueryString}";
                            }
                        },
                        OnCreatingTicket = async context =>
                        {
                            var request = new HttpRequestMessage(HttpMethod.Get, context.Options.UserInformationEndpoint);
                            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", context.AccessToken);
                            var response = await context.Backchannel.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, context.HttpContext.RequestAborted);
                            response.EnsureSuccessStatusCode();
                            var json = JsonDocument.Parse(await response.Content.ReadAsStringAsync());
                            var x = json.RootElement;
                            context.RunClaimActions(json.RootElement);
                        }
                    };
                });
            }

            if (externalProviderConfiguration.UsePowerSchoolProvider)
            {
                authenticationBuilder.AddOAuth("PowerSchool", options =>
                {
                    options.ClientId = externalProviderConfiguration.PowerSchoolClientId;
                    options.ClientSecret = externalProviderConfiguration.PowerSchoolClientSecret;
                    options.CallbackPath = externalProviderConfiguration.PowerSchoolCallbackPath;
                    options.AuthorizationEndpoint = "https://powerschool.com/oauth";
                    options.TokenEndpoint = "https://canvas.com/oauth/token";
                });
            }

            if (externalProviderConfiguration.UseD2lProvider)
            {
                authenticationBuilder.AddOAuth("D2l", options =>
                {
                    options.ClientId = externalProviderConfiguration.D2lClientId;
                    options.ClientSecret = externalProviderConfiguration.D2lClientSecret;
                    options.CallbackPath = externalProviderConfiguration.D2lCallbackPath;
                    options.AuthorizationEndpoint = "https://login-finder.d2l.com";
                    options.TokenEndpoint = "https://login-finder.d2l.com";
                });
            }

            if (externalProviderConfiguration.UseLoginGovProvider)
            {
                authenticationBuilder.AddOpenIdConnect("LoginGov", options =>
                {

                    options.ClientId = externalProviderConfiguration.LoginGovId;
                    options.Authority = externalProviderConfiguration.LoginGovAuthority;
                    options.Scope.Add("openid email");
                    options.ResponseType = "code";
                    options.UsePkce = true;
                    options.Events = new OpenIdConnectEvents
                    {

                        OnRedirectToIdentityProvider = context =>
                        {
                            context.ProtocolMessage.SetParameter("acr_values", "http://idmanagement.gov/ns/assurance/loa/1");
                            return Task.CompletedTask;
                        },
                        //OnAuthorizationCodeReceived = async context =>
                        //{
                        //    // Create and sign your JWT for the client_assertion
                        //    var clientAssertion = CreateClientAssertionJwt(context.Options.ClientId, context.Options.Authority);

                        //    var tokenRequestParameters = new Dictionary<string, string>
                        //    {
                        //        {"grant_type", "authorization_code"},
                        //        {"code", context.ProtocolMessage.Code},
                        //        {"client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer"},
                        //        {"client_assertion", clientAssertion}
                        //    };

                        //    var requestMessage = new HttpRequestMessage(HttpMethod.Post, $"{externalProviderConfiguration.LoginGovAuthority}/api/openid_connect/token")
                        //    {
                        //        Content = new FormUrlEncodedContent(tokenRequestParameters)
                        //    };

                        //    // Send the request to the token endpoint
                        //    var response = await context.Backchannel.SendAsync(requestMessage, context.HttpContext.RequestAborted);
                        //    var xx = await response.Content.ReadAsStringAsync();
                        //    response.EnsureSuccessStatusCode();

                        //    var payload = JsonDocument.Parse(await response.Content.ReadAsStringAsync()).RootElement;
                        //    context.HandleCodeRedemption(payload.GetProperty("access_token").GetString(), payload.GetProperty("id_token").GetString());
                        //}
                    };
                });
            }
        }

        /// <summary>
        /// Register middleware for localization
        /// </summary>
        /// <param name="app"></param>
        public static void UseMvcLocalizationServices(this IApplicationBuilder app)
        {
            var options = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(options.Value);
        }

        /// <summary>
        /// Add authorization policies
        /// </summary>
        /// <param name="services"></param>
        /// <param name="rootConfiguration"></param>
        public static void AddAuthorizationPolicies(this IServiceCollection services,
                IRootConfiguration rootConfiguration)
        {
            services.AddAuthorization(options =>
            {
                options.AddPolicy(AuthorizationConsts.AdministrationPolicy,
                    policy => policy.RequireRole(rootConfiguration.AdminConfiguration.AdministrationRole));
            });
        }

        public static void AddIdSHealthChecks<TConfigurationDbContext, TPersistedGrantDbContext, TIdentityDbContext, TDataProtectionDbContext>(this IServiceCollection services, IConfiguration configuration)
            where TConfigurationDbContext : DbContext, IAdminConfigurationDbContext
            where TPersistedGrantDbContext : DbContext, IAdminPersistedGrantDbContext
            where TIdentityDbContext : DbContext
            where TDataProtectionDbContext : DbContext, IDataProtectionKeyContext
        {
            var configurationDbConnectionString = configuration.GetConnectionString(ConfigurationConsts.ConfigurationDbConnectionStringKey);
            var persistedGrantsDbConnectionString = configuration.GetConnectionString(ConfigurationConsts.PersistedGrantDbConnectionStringKey);
            var identityDbConnectionString = configuration.GetConnectionString(ConfigurationConsts.IdentityDbConnectionStringKey);
            var dataProtectionDbConnectionString = configuration.GetConnectionString(ConfigurationConsts.DataProtectionDbConnectionStringKey);

            var healthChecksBuilder = services.AddHealthChecks()
                .AddDbContextCheck<TConfigurationDbContext>("ConfigurationDbContext")
                .AddDbContextCheck<TPersistedGrantDbContext>("PersistedGrantsDbContext")
                .AddDbContextCheck<TIdentityDbContext>("IdentityDbContext")
                .AddDbContextCheck<TDataProtectionDbContext>("DataProtectionDbContext");

            var serviceProvider = services.BuildServiceProvider();
            var scopeFactory = serviceProvider.GetRequiredService<IServiceScopeFactory>();
            using var scope = scopeFactory.CreateScope();
            var configurationTableName = DbContextHelpers.GetEntityTable<TConfigurationDbContext>(scope.ServiceProvider);
            var persistedGrantTableName = DbContextHelpers.GetEntityTable<TPersistedGrantDbContext>(scope.ServiceProvider);
            var identityTableName = DbContextHelpers.GetEntityTable<TIdentityDbContext>(scope.ServiceProvider);
            var dataProtectionTableName = DbContextHelpers.GetEntityTable<TDataProtectionDbContext>(scope.ServiceProvider);

            var databaseProvider = configuration.GetSection(nameof(DatabaseProviderConfiguration)).Get<DatabaseProviderConfiguration>();
            switch (databaseProvider.ProviderType)
            {
                case DatabaseProviderType.SqlServer:
                    healthChecksBuilder
                        .AddSqlServer(configurationDbConnectionString, name: "ConfigurationDb",
                            healthQuery: $"SELECT TOP 1 * FROM dbo.[{configurationTableName}]")
                        .AddSqlServer(persistedGrantsDbConnectionString, name: "PersistentGrantsDb",
                            healthQuery: $"SELECT TOP 1 * FROM dbo.[{persistedGrantTableName}]")
                        .AddSqlServer(identityDbConnectionString, name: "IdentityDb",
                            healthQuery: $"SELECT TOP 1 * FROM dbo.[{identityTableName}]")
                        .AddSqlServer(dataProtectionDbConnectionString, name: "DataProtectionDb",
                            healthQuery: $"SELECT TOP 1 * FROM dbo.[{dataProtectionTableName}]");

                    break;
                case DatabaseProviderType.PostgreSQL:
                    healthChecksBuilder
                        .AddNpgSql(configurationDbConnectionString, name: "ConfigurationDb",
                            healthQuery: $"SELECT * FROM \"{configurationTableName}\" LIMIT 1")
                        .AddNpgSql(persistedGrantsDbConnectionString, name: "PersistentGrantsDb",
                            healthQuery: $"SELECT * FROM \"{persistedGrantTableName}\" LIMIT 1")
                        .AddNpgSql(identityDbConnectionString, name: "IdentityDb",
                            healthQuery: $"SELECT * FROM \"{identityTableName}\" LIMIT 1")
                        .AddNpgSql(dataProtectionDbConnectionString, name: "DataProtectionDb",
                            healthQuery: $"SELECT * FROM \"{dataProtectionTableName}\"  LIMIT 1");
                    break;
                case DatabaseProviderType.MySql:
                    healthChecksBuilder
                        .AddMySql(configurationDbConnectionString, name: "ConfigurationDb")
                        .AddMySql(persistedGrantsDbConnectionString, name: "PersistentGrantsDb")
                        .AddMySql(identityDbConnectionString, name: "IdentityDb")
                        .AddMySql(dataProtectionDbConnectionString, name: "DataProtectionDb");
                    break;
                default:
                    throw new NotImplementedException($"Health checks not defined for database provider {databaseProvider.ProviderType}");
            }
        }


        private static string CreateClientAssertionJwt(string clientId, string audience)
        {
            var pem = "";
            var rsa = RSA.Create();
            rsa.ImportFromPem(pem);
            var credentials = new SigningCredentials(new RsaSecurityKey(rsa), SecurityAlgorithms.RsaSha256);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Issuer = clientId,
                Subject = new ClaimsIdentity(new[] { new Claim("sub", clientId) }),
                Audience = audience + "/api/openid_connect/token",
                Expires = DateTime.UtcNow.AddMinutes(5),
                SigningCredentials = credentials
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
