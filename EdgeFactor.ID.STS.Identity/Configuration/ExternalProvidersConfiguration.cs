﻿namespace EdgeFactor.ID.STS.Identity.Configuration
{
    public class ExternalProvidersConfiguration
    {
        public bool UseGoogleProvider { get; set; }
        public string GoogleClientId { get; set; }
        public string GoogleClientSecret { get; set; }
        public bool UseFacebookProvider { get; set; }
        public string FacebookClientId { get; set; }
        public string FacebookClientSecret { get; set; }
        public bool UseMicrosoftProvider { get; set; }
        public string MicrosoftClientId { get; set; }
        public string MicrosoftClientSecret { get; set; }
        public bool UseCleverProvider { get; set; }
        public string CleverClientId { get; set; }
        public string CleverClientSecret { get; set; }
        public string CleverCallbackPath { get; set; }
        public bool UseClassLinkProvider { get; set; }
        public string ClassLinkClientId { get; set; }
        public string ClassLinkClientSecret { get; set; }
        public string ClassLinkCallbackPath { get; set; }
        public bool UseCanvasProvider { get; set; }
        public string CanvasClientId { get; set; }
        public string CanvasClientSecret { get; set; }
        public string CanvasCallbackPath { get; set; }
        public bool UsePowerSchoolProvider { get; set; }
        public string PowerSchoolClientId { get; set; }
        public string PowerSchoolClientSecret { get; set; }
        public string PowerSchoolCallbackPath { get; set; }
        public bool UseD2lProvider { get; set; }
        public string D2lClientId { get; set; }
        public string D2lClientSecret { get; set; }
        public string D2lCallbackPath { get; set; }
        public bool UseLoginGovProvider { get; set; }
        public string LoginGovId { get; set; }
        public string LoginGovAuthority { get; set; }
    }
}