﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace EdgeFactor.ID.STS.Identity.ViewModels.Account
{
    public record D2LSSOAuth(Guid Id, Guid OrganizationId, string ExternalSystem, string? ExternalId, string? ExternalAuthId, string? ExternalAuthSecret, string? ExternalUrl)
    {
    }

    public record D2LSSOToken(
        [property: JsonPropertyName("access_token")] string AccessToken,
        [property: JsonPropertyName("token_type")] string TokenType,
        [property: JsonPropertyName("scope")] string Scope,
        [property: JsonPropertyName("expires_in")] int ExpiresIn,
        [property: JsonPropertyName("refresh_token")] string RefreshToken
    );

    public record D2LSSOUserInfo(
    [property: JsonPropertyName("Identifier")] string Identifier,
    [property: JsonPropertyName("FirstName")] string FirstName,
    [property: <PERSON><PERSON><PERSON>roper<PERSON>Name("LastName")] string LastName,
    [property: Json<PERSON>ropertyName("Pronouns")] object Pronouns,
    [property: <PERSON><PERSON><PERSON>roper<PERSON>N<PERSON>("UniqueName")] string UniqueName,
    [property: <PERSON><PERSON><PERSON>ropertyName("ProfileIdentifier")] string ProfileIdentifier
    )
    {
        public D2LSSOUserInfo() : this(null, null, null, null, null, null) { }
    };


    public class D2LSSOProfileInfo
    {
        [property: JsonPropertyName("ProfileIdentifier")]
        public string ProfileIdentifier { get; set; }

        [property: JsonPropertyName("Nickname")]
        public string Nickname { get; set; }
        [property: JsonPropertyName("Birthday")] public string Birthday { get; set; }
        [property: JsonPropertyName("HomeTown")] public string HomeTown { get; set; }
        [property: JsonPropertyName("Email")] public string Email { get; set; }
        [property: JsonPropertyName("HomePage")] public string HomePage { get; set; }
        [property: JsonPropertyName("HomePhone")] public string HomePhone { get; set; }
        [property: JsonPropertyName("BusinessPhone")] public string BusinessPhone { get; set; }
        [property: JsonPropertyName("MobilePhone")] public string MobilePhone { get; set; }
        [property: JsonPropertyName("FaxNumber")] public string FaxNumber { get; set; }
        [property: JsonPropertyName("Address1")] string Address1 { get; set; }
        [property: JsonPropertyName("Address2")] public string Address2 { get; set; }
        [property: JsonPropertyName("City")] public string City { get; set; }
        [property: JsonPropertyName("Province")] public string Province { get; set; }
        [property: JsonPropertyName("PostalCode")] public string PostalCode { get; set; }
        [property: JsonPropertyName("Country")] public string Country { get; set; }
        [property: JsonPropertyName("Company")] public string Company { get; set; }
        [property: JsonPropertyName("JobTitle")] public string JobTitle { get; set; }
        [property: JsonPropertyName("HighSchool")] public string HighSchool { get; set; }
        [property: JsonPropertyName("University")] public string University { get; set; }
        [property: JsonPropertyName("Hobbies")] public string Hobbies { get; set; }
        [property: JsonPropertyName("FavMusic")] public string FavMusic { get; set; }
        [property: JsonPropertyName("FavTVShows")] public string FavTVShows { get; set; }
        [property: JsonPropertyName("FavMovies")] public string FavMovies { get; set; }
        [property: JsonPropertyName("FavBooks")] public string FavBooks { get; set; }
        [property: JsonPropertyName("FavQuotations")] public string FavQuotations { get; set; }
        [property: JsonPropertyName("FavWebSites")] public string FavWebSites { get; set; }
        [property: JsonPropertyName("FutureGoals")] public string FutureGoals { get; set; }
        [property: JsonPropertyName("FavMemory")] public string FavMemory { get; set; }
        [property: JsonPropertyName("SocialMediaUrls")] IReadOnlyList<SocialMediaUrl> SocialMediaUrls { get; set; }
    }


    public record SocialMediaUrl(
        [property: JsonPropertyName("Name")] string Name,
        [property: JsonPropertyName("Url")] string Url
    );

}
