{"ConnectionStrings": {"ConfigurationDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "IdentityDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "CertificateConfiguration": {"UseTemporarySigningKeyForDevelopment": true, "CertificateStoreLocation": "LocalMachine", "CertificateValidOnly": true, "UseSigningCertificateThumbprint": false, "SigningCertificateThumbprint": "", "UseSigningCertificatePfxFile": false, "SigningCertificatePfxFilePath": "", "SigningCertificatePfxFilePassword": "", "UseValidationCertificatePfxFile": false, "ValidationCertificatePfxFilePath": "", "ValidationCertificatePfxFilePassword": "", "UseValidationCertificateThumbprint": false, "ValidationCertificateThumbprint": "", "UseSigningCertificateForAzureKeyVault": false, "UseValidationCertificateForAzureKeyVault": false}, "RegisterConfiguration": {"Enabled": true, "IdentitySource": 1, "EFApiBaseUrl": "https://edgefactorapi.cbos.co.za/v1", "EFBaseUrl": "https://edgefactor.cbos.co.za"}, "MailTemplates": {"NewRegistration": "d-cbaabf2b318847eb8d4f56133044b968", "NewAccount": "d-f7e83b62b92244d89fa9a584986883d1", "PasswordReset": "d-b7ef3470b749468aad28bd4073c5a569", "EmailVerification": "d-27b8a11c61a54166980b1928daf3ef89"}, "Urls": {"TermsUrl": "https://edgefactor.cbos.co.za/about", "PrivacyUrl": "https://edgefactor.cbos.co.za/about", "ContactUrl": "https://edgefactor.cbos.co.za/about", "VerificationUrl": "", "returnUrl": "https://edgefactor.cbos.co.za/auth-callback/external"}, "ExternalProvidersConfiguration": {"UseGoogleProvider": true, "GoogleClientId": "************-5ri7ro6oelfah6p9r1ffhalu461h72jd.apps.googleusercontent.com", "GoogleClientSecret": "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", "UseFacebookProvider": true, "FacebookClientId": "***************", "FacebookClientSecret": "********************************", "UseMicrosoftProvider": true, "MicrosoftClientId": "2c8408c2-377e-46b1-aba9-95cd04eadc0c", "MicrosoftClientSecret": "****************************************", "UseCleverProvider": true, "CleverClientId": "c778a7bb8ec7e93bee6f", "CleverClientSecret": "53ddd26946f41e51516423f1103c48fa91675256", "CleverCallbackPath": "/signin-oauth", "UseClassLinkProvider": false, "ClassLinkClientId": "c155719601460506b52202fac7efd06e5cd890b276ca24", "ClassLinkClientSecret": "0f48e619d144dec61673e98cbce17f73", "ClasslinkCallbackPath": "/signin-oauth", "UseCanvasProvider": false, "CanvasClientId": "", "CanvasClientSecret": "", "CanvasCallbackPath": "/signin-oauth", "UsePowerSchoolProvider": false, "PowerSchoolClientId": "", "PowerSchoolClientSecret": "", "PowerSchoolCallbackPath": "/signin-oauth", "UseLoginGovProvider": true, "LoginGovId": "urn:gov:gsa:openidconnect.profiles:sp:sso:arkansas:edgefactor", "LoginGovAuthority": "https://idp.int.identitysandbox.gov"}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "*********************************************************************", "SourceEmail": "<EMAIL>", "SourceName": "EdgeFactor"}, "LoginConfiguration": {"ResolutionPolicy": "Username"}, "AdminConfiguration": {"PageTitle": "", "HomePageLogoUri": "~/images/EFLogo_White.png", "FaviconUri": "~/favicon.ico", "Theme": null, "CustomThemeCss": "/dist/css/theme.css", "IdentityAdminBaseUrl": "https://edgefactoridadmin.cbos.co.za", "AdministrationRole": "Admin"}, "CspTrustedDomains": ["www.gravatar.com", "fonts.googleapis.com", "fonts.gstatic.com"], "CultureConfiguration": {"Cultures": [], "DefaultCulture": null}, "AdvancedConfiguration": {"IssuerUri": ""}, "BasePath": "", "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": true}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "GitHubCallbackPath": "", "UseClientCredentials": true, "IdentityServerCertificateName": "", "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}, "Environment": {"Location": "United States", "LocationId": "4E008CFD-B452-40A5-8FBD-3DA3976591F8"}}