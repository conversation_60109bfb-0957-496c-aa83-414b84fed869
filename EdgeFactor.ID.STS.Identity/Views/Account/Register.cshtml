﻿@inject IViewLocalizer Localizer
@using Microsoft.AspNetCore.Mvc.Localization
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.RegisterViewModel
@{
    ViewData["Title"] = Localizer["Title"];
}
<head>

</head>

<div class="register-page">
    @await Html.PartialAsync("_ValidationSummary")
    <div class="row align-items-center justify-content-center">
        @if (Model.EnableLocalLogin)
        {
            <div class="card col-11 col-sm-10 col-lg-5">
                <div class="card-body">
                    <div class="page-header">
                        <img style="width:-webkit-fill-available" src="~/images/EFLogo_White.png" />
                        <h4>@Localizer["SubTitle"]</h4>
                    </div>
                    <form class="form" asp-controller="Account" asp-action="Register" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post" >
                        <input type="hidden" asp-for="ReturnUrl" />

                        <div class="provider-container">
                            @if (Model.VisibleExternalProviders.Any())
                            {
                                <div class="col-sm-12">
                                    <div class="provider">
                                        @foreach (var provider in Model.VisibleExternalProviders)
                                        {                                       
                                            <div>
                                                <a class="link btn"
                                                    asp-action="ExternalLogin"
                                                    asp-route-provider="@provider.AuthenticationScheme"
                                                    asp-route-returnUrl="@Model.ReturnUrl">
                                                    <img style="width:25px" src="~/images/@provider.DisplayName-logo.png" />
                                                </a>
                                            </div>                                          
                                        }
                                    </div>
                                </div>
                            }
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-12" style="text-align: center;">
                                <h6 style="margin-bottom: 0px;" class="login-with-text"> or </h6>
                            </div>
                        </div>
            
                        <fieldset>
                            <div class="form-container">
                                <div class="top-header">            
                                    <h1> Sign up with your email address </h1>                       
                                </div>

                                <div class="row-container">
                                    <div class="inner-container">
                                        <div>
                                            <input class="form-control" placeholder="Email" asp-for="Email" aria-label="Email" aria-describedby="input-Email" autofocus>
                                        </div>                          
                                        <span asp-validation-for="Email" class="text-danger"></span>
                                    </div>
                                    <div>
                                        <h2> You'll need to confirm that this email belongs to you. </h2>
                                    </div>
                                </div>

                                <div class="grid-container">
                                    <div>
                                        <input class="form-control" placeholder="First Name" asp-for="FirstName" aria-label="FirstName" aria-describedby="input-FirstName" autofocus>
                                        <span asp-validation-for="FirstName" class="text-danger"></span>
                                    </div>                          
                                                            
                                    <div>
                                        <input class="form-control" placeholder="Last Name" asp-for="LastName" aria-label="LastName" aria-describedby="input-LastName" autofocus>
                                         <span asp-validation-for="LastName" class="text-danger"></span>  
                                    </div>                                                                              
                                </div>

                                <div class="row-container-password">
                                    <div class="inner-container">
                                        <input id="password" class="form-control" type="password" placeholder="Password" asp-for="Password" aria-label="Password" aria-describedby="input-Password" autofocus >
                                        <div class="eye-btn">
                                            <i class="fa fa-eye-slash" id="togglePassword"></i>
                                            <script>
                                                const togglePassword = document.querySelector("#togglePassword");
                                                const password = document.querySelector("#password");
                                                togglePassword.addEventListener("click", function () {
                                                    const type = password.getAttribute("type") === "password" ? "text" : "password";
                                                    password.setAttribute("type", type);
                                                    this.classList.toggle("fa-eye");
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <span asp-validation-for="Password" class="text-danger"></span>                                  
                                </div>
                        
                                <div class="birth-form-container">
                                    <div>
                                        <h3> Date of Birth </h3>
                                    </div>
                            
                                    <div class="grid-container"> 
                                        <div>
                                            <select class="form-control" asp-for="Month" aria-label="Month" aria-describedby="input-Month" autofocus>
                                                <option value="" disabled selected> Month </option>
                                                    <option value="January">January</option>
                                                    <option value="February">February</option>
                                                    <option value="March">March</option>
                                                    <option value="April">April</option>
                                                    <option value="May">May</option>
                                                    <option value="June">June</option>
                                                    <option value="July">July</option>
                                                    <option value="August">August</option>
                                                    <option value="September">September</option>
                                                    <option value="October">October</option>
                                                    <option value="November">November</option>
                                                    <option value="December">December</option>
                                            </select> 
                                            <span asp-validation-for="Month" class="text-danger"></span>
                                        </div>
                                        <div>                                                                
                                            <select class="form-control" id='year-dropdown' asp-for="Year" aria-label="Year" aria-describedby="input-Year" autofocus>
                                                <option value="" disabled selected> Year </option>
                                            </select>
                                                   <script>
                                                    let dateDropdown = document.getElementById('year-dropdown');
                                                    let currentYear = new Date().getFullYear();
                                                    let earliestYear = 1930;

                                                    while (currentYear >= earliestYear) {
                                                        let dateOption = document.createElement('option');
                                                        dateOption.text = currentYear;
                                                        dateOption.value = currentYear;
                                                        dateDropdown.add(dateOption);
                                                        currentYear -= 1;
                                                    }
                                                    </script>
                                            <span asp-validation-for="Year" class="text-danger"></span>
                                        </div>
                                        <div>
                                            <input class="form-control" placeholder="Country" asp-for="Country" aria-label="Country" aria-describedby="input-Country" autofocus>
                                            <span asp-validation-for="Country" class="text-danger"></span>
                                        </div>                                                                                   
                                        <div>
                                            <input class="form-control" placeholder="Postal Code" asp-for="PostalCode" aria-label="PostalCode" aria-describedby="input-PostalCode" autofocus>
                                            <span asp-validation-for="PostalCode" class="text-danger"></span>    
                                        </div>                                                       
                                    </div>
                                    <div>
                                        <h2> We collect date of birth and location for account authentication and recovery purposes, and to ensure age-appropriate access to content. </h2>
                                    </div>
                                </div>
                            </div>
                                       
                            <div class="consent-container">
                                <div class="header-container">
                                    <h3>Edge Factor may keep me informed with <a href="">personalized emails</a> about my career journey and the services. We won't send any marketing emails and you can opt out at any time. See our <a href="">Privacy Policy</a> for more details</h3>
                                    <h3>By creating an account, I agree that I have read and accepted the <a href="https://edgefactor.com/legal/privacy-policy">Privacy</a> and <a href="https://edgefactor.com/legal/terms-of-use">Term of Use</a> policies.</h3>
                                </div>
                                <div class="button-container"> 
                                    <button type="submit" class="btn btn-primary" style="width:-webkit-fill-available; color:black; border-radius: 3px;">Sign Up</button>                                 
                                </div>
                            </div>
        
                            <div class="footer">
                                <div class="header-container">
                                    <span class="footer-login">Already have an account? <a style="padding-left: 5px" asp-action="Login" asp-controller="Account" asp-route-returnUrl="@Model.ReturnUrl" value="register">LOG IN</a></span>
                                </div>
                            </div>            
                        </fieldset>
                    </form>
                </div>
            </div>
        }

        @if (!Model.EnableLocalLogin && !Model.VisibleExternalProviders.Any())
        {
            <div class="alert alert-warning">
                <strong>@Localizer["InvalidRequest"]</strong>
                @Localizer["NoSchema"]
            </div>
        }
    </div>
</div>

