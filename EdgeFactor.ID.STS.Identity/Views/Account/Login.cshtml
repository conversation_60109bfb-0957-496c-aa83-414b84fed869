﻿@using Microsoft.AspNetCore.Mvc.Localization
@using EdgeFactor.ID.STS.Identity.Configuration.Interfaces
@using EdgeFactor.ID.STS.Identity.Helpers.Localization
@inject IViewLocalizer Localizer
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.LoginViewModel
@inject IRootConfiguration RootConfiguration

<div class="login-page">

    <style>
        .cc-window{
            display: none !important;
        }
    </style>


    @await Html.PartialAsync("_ValidationSummary")

    <div class="row align-items-center justify-content-center">

        @if (Model.EnableLocalLogin)
        {
            <div class="card col-11 col-sm-10 col-lg-5">
                <div class="card-body">
                    <div class="page-header">
                        <img style="width:-webkit-fill-available" src="~/images/EFLogo_White.png" />
                        <h2 class="center">
                            Login to continue.
                        </h2>
                    </div>
                    <form asp-route="Login">
                        <input type="hidden" asp-for="ReturnUrl" />

                        <fieldset>
                            <div class="form-group row">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <input class="form-control" placeholder="@Localizer[LoginPolicyResolutionLocalizer.GetUserNameLocalizationKey(Model.LoginResolutionPolicy)]" asp-for="Username" aria-label="Username" aria-describedby="input-username" autofocus>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <input style="padding-right:30px" id="password" type="password" class="form-control" placeholder="@Localizer["Password"]" aria-label="Password" aria-describedby="input-password" asp-for="Password" autocomplete="off">
                                        <i class="fa toggle-password-eye fa-eye-slash" id="togglePassword"></i>
                                        <script>
                                            const togglePassword = document.querySelector('#togglePassword');
                                            const password = document.querySelector('#password');

                                            togglePassword.addEventListener('click', function (e) {
                                                // toggle the type attribute
                                                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                                                password.setAttribute('type', type);
                                                // toggle the eye slash icon
                                                if(type ==='text'){
                                                    this.classList.remove('fa-eye-slash');
                                                    this.classList.add('fa-eye');
                                                }else{
                                                    this.classList.remove('fa-eye');
                                                    this.classList.add('fa-eye-slash');
                                                }
                                            });
                                        </script>
                                    </div>
                                </div>
                            </div>

                            @if (Model.AllowRememberLogin)
                            {
                                <div class="form-group login-remember row">
                                    <div class="col-sm-6">
                                        <span class="remember"> <input asp-for="RememberLogin" type="checkbox">  @Localizer["Remember"]</span>
                                    </div>
                                    <div class="col-sm-6 align-right">
                                        <a class="forgot" asp-action="ForgotPassword" asp-controller="Account" value="forgot">@Localizer["Forgot"]?</a>
                                    </div>
                                </div>
                            }

                            <!--Button-->
                            <div class="form-group row">
                                <div class="col-sm-12 center">
                                    <button class="btn btn-primary btn-block" name="button" value="login">@Localizer["Login"]</button>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-12" style="text-align: center">
                                    <h6 class="login-with-text"> or login with</h6>
                                </div>
                            </div>
                            <div class="form-group row">
                                @if (Model.VisibleExternalProviders.Any())
                                {
                                    <div class="col-sm-12">
                                        <div class="form-group row">
                                            @foreach (var provider in Model.VisibleExternalProviders)
                                            {
                                                <div class="col-sm-6 col-6">
                                                    <a class="btn btn-light external-login"
                                           asp-action="ExternalLogin"
                                           asp-route-provider="@provider.AuthenticationScheme"
                                           asp-route-returnUrl="@Model.ReturnUrl">
                                                        <img class="small-width" src="~/images/@provider.DisplayName-logo.png" />
                                                        @if (@provider.DisplayName == "D2l")
                                                        {
                                                            @:D2L | Brightspace
                                                        }
                                                        else
                                                        {
                                                            @provider.DisplayName
                                                        }
                                                    </a>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }

                            </div>

                            <div class="form-group row">
                                <div class="col-sm-12" style="text-align: center">
                                    <h6 class="register">
                                        Don't have an account ?
                                        <a asp-action="Register" asp-controller="Account" asp-route-returnUrl="@Model.ReturnUrl" value="register">@Localizer["Register"]</a>
                                    </h6>
                                </div>
                            </div>
                        </fieldset>
                    </form>
                </div>
            </div>
        }

        @if (!Model.EnableLocalLogin && !Model.VisibleExternalProviders.Any())
        {
            <div class="alert alert-warning">
                <strong>@Localizer["InvalidRequest"]</strong>
                @Localizer["NoSchema"]
            </div>
        }
    </div>

</div>
