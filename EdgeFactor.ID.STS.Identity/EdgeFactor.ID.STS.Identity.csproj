﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Version>2.0.1</Version>
        <Authors><PERSON></Authors>
        <UserSecretsId>9c91d295-54c5-4d09-9bd6-fa56fb74011b</UserSecretsId>
        <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <DockerfileContext>..\..</DockerfileContext>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.MySql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="6.0.5" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="6.0.5" />
        <PackageReference Include="LtiAdvantage" Version="0.2.0-alpha.0" />
        <PackageReference Include="LtiAdvantage.IdentityModel" Version="0.2.0-alpha.0" />
        <PackageReference Include="LtiAdvantage.IdentityServer4" Version="0.2.0-alpha.0" />        
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.OAuth" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Identity.Web" Version="1.25.6" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.0" />      
        <PackageReference Include="IdentityServer4.AspNetIdentity" Version="4.1.2" />
        <PackageReference Include="IdentityServer4.EntityFramework" Version="4.1.2" />
        <PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
        <PackageReference Include="Serilog" Version="2.12.0" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="5.0.1" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="5.2.2" />
        <PackageReference Include="Skoruba.AuditLogging.EntityFramework" Version="1.0.0" />
        <PackageReference Include="Skoruba.IdentityServer4.Shared.Configuration" Version="2.1.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.Shared\EdgeFactor.ID.Admin.EntityFramework.Shared.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Shared\EdgeFactor.ID.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="appsettings.CA-Staging.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.CA.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.Development.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.Uat.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.USA-Staging.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.USA.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Update="serilog.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.ru.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.zh.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.sv.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.fi.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.fr.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.fa.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.es.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.de.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.da.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Views\Account\RegisterConfirmation.en.resx">
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
    </ItemGroup>

</Project>





















