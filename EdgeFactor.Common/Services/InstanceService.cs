using EdgeFactor.Common.DB.Models;
using EdgeFactor.Common.Extensions;
using EdgeFactor.Common.Models.Instance;
using EdgeFactor.Common.Models.Organization;
using EdgeFactor.Common.Models.Row;
using EdgeFactor.Common.Models.Template;
using Microsoft.EntityFrameworkCore;
using Component = EdgeFactor.Common.Models.Template.Component;
using Instances = EdgeFactor.Common.DB.Models.Instances;
using SectionTypes = EdgeFactor.Common.Enums.SectionTypes;

namespace EdgeFactor.Common.Services
{
    public interface IInstanceService
    {
        Task<Instance?> GetInstance(Guid? userId, Guid? instanceId, string? slug, Guid? organizationId, bool isGuest, CancellationToken token, bool isScorm = false);
        Task<Instances?> UpdateInstance(Guid? userId, Guid id, InstanceIn instance);
        Task<List<InstanceRowLite>> GetInstanceSectionComponentValue(Guid instanceId, Guid componentId);
        Task<RepositoryBuilderDashboard> CreateInstance(Guid userId, Instance instance, CancellationToken token);
        Task<Guid> GetFeatureIdByFeatureTypeId(Guid id);
        Task<RepositoryBuilderDashboard> CreateInstanceByFeatureId(Guid userId, Guid orgId, Guid featureId, CancellationToken token);
        Task<bool> CreateInstances(Guid userId, Instance[] instances);
        Task<bool> DeleteInstance(Guid? userId, Guid id);
        Task<IEnumerable<Instance>> SearchInstance(Guid? userId, string search, Guid? orgId);
        Task<IEnumerable<Instance>> GetInstances(string? query, Guid? organizationId);
        Task<Instance?> UpdateInstanceStatus(Guid id, string status, Guid userId, DateTime? dueDate, CancellationToken token);
        Task<Guid?> GetDefaultAssetId(Guid id, string systemProperty);
        Task<bool> SetInstanceJoinCode(Guid? userId, Guid instanceId, bool isJoinCodeInstance);
        Task<MyInstancesResult> GetMyInstances(string? query, Guid? typeId, Guid? userId, Guid? instanceId, CancellationToken token);
        Task<IEnumerable<RowLite?>> GetInstanceRowManagerComponentRows(Guid? userId, Guid instanceId, CancellationToken token);
        Task<Guid?> GetPrimaryEductator(Guid? instanceId);
        Task<Instance> GetById(Guid id);
        Task<Guid?> GetInstanceOwner(Guid? id);
        Task<InstanceTemplate> GetInstanceTemplateById(Guid? userId, Guid instanceId, Guid templateId, bool isViewField = false, bool isBuilderField = false, bool isScorm = false);
        Task<List<InstanceSection>> GetInstanceSections(Guid? userId, Guid instanceId, bool isScorm = false);
        Task<InstanceSectionsAndComponentsResult> GetInstanceSectionsAndComponents(Guid? userId, Guid? instanceId, bool isViewField = false, bool isHoverField = false, bool isPreviewField = false, bool isRequiredField = false, bool isScorm = false);
        Task<Guid> GetInstanceIdBySlug(string slug);
        Task<InstanceSection> AddInstanceSection(Guid? userId, InstanceSectionIn instanceSectionIn);
        Task<bool> UpdateInstanceSections(Guid? userId, IEnumerable<InstanceSectionIn> instanceSectionsIn, bool updateSortOrder = false);
        Task PersistSectionToInstances(Section section, CancellationToken token);
        Task PersistComponentToInstances(Component component, Guid sectionId, Guid userId, CancellationToken token);
        Task<InstanceSectionComponentResult> UpdateInstanceSectionComponent(Guid userid, Guid instanceSectionComponentId, InstanceSectionComponentValue instanceSectionComponentValue, CancellationToken token);
        Task<List<Component>?> GetDynamicSectionComponentTypes(Guid sectionId);
        Task<InstanceSectinComponentAddResult> AddInstanceSectionComponents(Guid instanceSectionId, List<Guid> componentIds);
        Task<bool> MarkInstanceSectionAsDeleted(Guid instanceSectionId, Guid userId);
        Task<bool> MarkInstanceSectionComponentAsDeleted(Guid instanceSectionComponentId, Guid userId);
        Task<bool> UpdateInstanceSectionComponents(Guid? userId, IEnumerable<InstanceSectionComponentIn> instanceSectionComponentsIn, CancellationToken token);
        Task<IEnumerable<InstanceSectionComponent>> GetInstanceSectionComponents(Guid instanceSectionId);
        Task<List<VwInstanceProgressGrade>?> GetInstancesGradingInfo(List<Guid> instanceIds);
        Task<bool> CheckInstanceSectionIsGraded(Guid instanceId, Guid instanceSectionId, Guid userId);
        Task<string?> GetInstanceOrgLocation(Guid instanceId, CancellationToken token);
        Task<bool> GetInstanceStatusById(Guid instanceId, CancellationToken token);
        Task<bool> GetInstanceStatusBySlug(string slug, CancellationToken token);
        Task<MyInstancesResult?> GetInstanceAssignments(Guid instanceId, bool onlyAssignments = false);
        Task<bool> AddInstanceAssignment(Guid parentInstanceId, Guid instanceId, Guid userId);
        Task<bool> DeleteInstanceAssignment(Guid instanceId, Guid parentInstanceId);
        Task<bool> MoveNonClassRoomContent(CancellationToken token);
        Task<bool> SyncParentAndChildUsers(Guid userId, Guid parentInstanceId);
        Task<IEnumerable<Organization>> GetOrganizationsByInstanceId(Guid id);
        Task<bool> AddOrganizationToInstance(Guid instanceId, Guid organizationId);
        Task<bool> RemoveOrganizationFromInstance(Guid instanceId, Guid organizationId);
    }
    public class InstanceService : IInstanceService
    {
        #region PROPERTIES
        private readonly EdgeFactorContext _db;
        private readonly ISystemPropertyService _systemPropertyService;
        #endregion

        #region CONSTRUCTORS
        public InstanceService(EdgeFactorContext dbContext, ISystemPropertyService systemPropertyService)
        {
            _db = dbContext;
            _systemPropertyService = systemPropertyService;
        }
        #endregion

        #region METHODS

        public IQueryable<Instances> GetInstanceIQueryable()
        {
            return _db.Instances
                .Include(x => x.CoverMediaAsset)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Tab)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Type)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabActions)
                            .ThenInclude(x => x.Action)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabPersonas)
                            .ThenInclude(x => x.Tag)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabEditActions)
                            .ThenInclude(x => x.Action)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabButtons)
                            .ThenInclude(x => x.FeatureTabButtonActions)
                                .ThenInclude(x => x.Action)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabButtons)
                            .ThenInclude(x => x.ButtonLink)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.FeatureTabRowTypes)
                            .ThenInclude(x => x.RowType)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureType)
                        .ThenInclude(x => x.SystemPropertyType)
                .Include(x => x.Organization)
                    .ThenInclude(x => x.ProductOrganizations)
                        .ThenInclude(x => x.Product)
                .Include(x => x.InstanceUsers)
                    .ThenInclude(x => x.Role)
                .AsSplitQuery();
        }

        public async Task<Instance?> GetInstance(Guid? userId, Guid? instanceId, string? slug, Guid? organizationId, bool isGuest, CancellationToken token, bool isScorm = false)
        {
            var instanceIQueryable = GetInstanceIQueryable();

            if (instanceId != null)
            {
                instanceIQueryable = instanceIQueryable.Where(x => x.Id == instanceId);
            }

            if (slug != null)
            {
                instanceIQueryable = instanceIQueryable.Where(x => (x.Feature.Slug == slug && x.IsDefault == true) || x.Slug == slug);

                if (slug.ToLower() == "my-portfolio" && await instanceIQueryable.AnyAsync(x => x.UserId == userId, token) == false)
                {
                    await CheckUserFeatureInstanceExists(userId, "Portfolio", token);

                    instanceIQueryable = GetInstanceIQueryable();
                    instanceIQueryable = instanceIQueryable.Where(x => (x.Feature.Slug == slug && x.IsDefault == true) || x.Slug == slug);
                }
            }

            Instances? instance;

            if (slug != null && slug.ToLower() == "my-portfolio")
            {
                instance = await instanceIQueryable.Where(x => x.UserId == userId).AsSplitQuery().FirstOrDefaultAsync(token);
            }
            else
            {
                instance = await instanceIQueryable.AsSplitQuery().FirstOrDefaultAsync(token);
            }

            if (instance == null)
            {
                return null;
            }

            if (instance.IconAssetId == null || instance.CoverMediaAssetId == null)
            {
                instance = await SetDefaultAsset(instance);
            }

            var userIsMember = false;
            var userIsEfAdmin = false;

            var efOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor Inc.").Select(x => x.Id).FirstOrDefaultAsync(token);

            userIsEfAdmin = await _db.OrganizationUsers.Include(x => x.Role).AnyAsync(x => x.UserId == userId && x.OrganizationId == efOrgId && x.Role.Name == "Administrator" && x.Status != "Deleted");

            if (instance == null || (instance.Status == "private" && instance.UserId != userId && userIsEfAdmin == false))
            {
                instance = await GetInstanceIQueryable().Where(x => (x.Feature.Slug == "404" && x.IsDefault == true) || x.Slug == "404").AsSplitQuery().FirstOrDefaultAsync(token);
                return new Instance(instance);
            }

            if (organizationId != null)
            {
                var networkTypeAcademicGroupId = new Guid("BC186B71-8FDF-4C15-86A8-EE196F964C69");

                var networkOrgIds = _db.NetworkOrganization
                                        .Include(x => x.Network)
                                        .Where(x => x.Network.NetworkTypeId == networkTypeAcademicGroupId && x.OrganizationId == organizationId && x.IsDeleted != true)
                                        .Select(x => x.Network.OrganizationId)
                                        .AsQueryable();

                userIsMember = await _db.OrganizationUsers
                                    .Include(x => x.Role)
                                    .AnyAsync(x => x.UserId == userId && (x.OrganizationId == efOrgId || networkOrgIds.Any(y => x.OrganizationId == y) || x.OrganizationId == organizationId) && x.Role.Name == "Administrator" && x.Status != "Deleted", token);
            }

            if (isScorm == false)
            {
                var productOrgUserRoles = _db.ProductOrganizationUsers
                            .Where(x => x.UserId == userId && x.Status != "Deleted");

                var productFeatureRolesIQueryable = _db.ProductFeatureRoles
                        .Include(x => x.Role)
                        .Where(x => x.ProductFeature.FeatureId == instance.FeatureId &&
                        productOrgUserRoles.Any(p => p.ProductOrganization.ProductId == x.ProductFeature.ProductId && p.RoleId == x.RoleId))
                        .OrderByDescending(x => x.ActionBw);

                var productFeatureRoles = await productFeatureRolesIQueryable.ToListAsync(token);


                if (userIsEfAdmin == false)
                {
                    instance.Feature.FeatureTabs = instance.Feature.FeatureTabs.Where(x => x.ShowForEfAdmin != true).ToList();
                }

                instance.Feature.FeatureTabs = instance.Feature.FeatureTabs.Where(x => (
                        x.FeatureTabActions?.Count == 0 ||
                        x.FeatureTabActions.Any(a =>
                            productFeatureRoles.Any(p => (p.ActionBw & a.Action.ActionBw) > 0)))
                    && (x.IsMember != true || userIsMember == true)
                    || (isGuest && x.ShowForGuest)
                )
                    .Select(x => new FeatureTabs()
                    {
                        Id = x.Id,
                        FeatureId = x.FeatureId,
                        TabId = x.TabId,
                        SortOrder = x.SortOrder,
                        ShowTab = x.ShowTab,
                        IsDefaultInstanceTab = x.IsDefaultInstanceTab,
                        PrimaryFilterId = x.PrimaryFilterId,
                        SecondaryFilterId = x.SecondaryFilterId,
                        TypeId = x.TypeId,
                        ButtonText = x.ButtonText,
                        IsMember = x.IsMember,
                        IsDeleted = x.IsDeleted,
                        Feature = x.Feature,
                        Tab = x.Tab,
                        Type = x.Type,
                        FeatureTabActions = x.FeatureTabActions,
                        FeatureTabButtons = x.FeatureTabButtons.Where(y => y.FeatureTabButtonActions.Any(z => productFeatureRoles.Any(a => (a.ActionBw & z.Action.ActionBw) > 0))).ToList(),
                        FeatureTabEditActions = x.FeatureTabEditActions,
                        FeatureTabRowTypes = x.FeatureTabRowTypes,
                        FeatureTabPersonas = x.FeatureTabPersonas,
                    })
                    .ToList();
            }

            var userPersonaIds = await _db.UserTags.Where(x => x.UserId == userId && x.Type == "Persona").Select(x => x.TagId).ToListAsync();

            if (!isGuest && instance.Feature.FeatureTabs.Any(x => x.FeatureTabPersonas.Any()) && userPersonaIds.Count > 0)
            {
                instance.Feature.FeatureTabs = instance.Feature.FeatureTabs.Where(x => x.FeatureTabPersonas.Any() == false || x.FeatureTabPersonas.Any(y => userPersonaIds.Contains(y.TagId))).ToList();
            }

            var earningCriteria = await _db.EarningCriteria
                .Include(x => x.EarningCriteriaContent)
                .Where(x => x.EarningCriteriaContent.Any(y => y.RefId == instance.Id))
                .Select(x => new EarningCriteria
                {
                    Id = x.Id,
                    InstanceId = x.InstanceId,
                    EarningCriteriaTypeId = x.EarningCriteriaTypeId,
                    SortOrder = x.SortOrder,
                    MinValue = x.MinValue,
                    MaxValue = x.MaxValue,
                    EarningCriteriaContent = x.EarningCriteriaContent
                })
                .FirstOrDefaultAsync(token);

            var isEnrolled = false;

            if (earningCriteria is not null)
            {
                isEnrolled = instance.InstanceUsers.Any(x => x.InstanceId == earningCriteria.InstanceId && x.UserId == userId && x.Status == "Active");
            }

            var userInstanceProgress = await _db.UserProgress.Where(x => x.InstanceId == instance.Id && x.UserId == userId).FirstOrDefaultAsync(token);

            return new Instance(instance, earningCriteria, isEnrolled, userId, userInstanceProgress);
        }

        public async Task<IEnumerable<Instance>> SearchInstance(Guid? userId, string search, Guid? orgId)
        {
            var notTheseFeatureTypes = new[] { "Accredited Learning Container Pages", "Modifiable Learning Container Pages" };
            var notTheseFeatureTypeIds = await _db.FeatureTypes
                .Where(x => notTheseFeatureTypes.Contains(x.Name))
                .Select(x => x.Id)
                .ToListAsync();

            var instancesQueryable = _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureType)
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Tab)
                .Where(x => x.Status != "deleted")
                .AsSplitQuery();

            if (!string.IsNullOrEmpty(search))
            {
                instancesQueryable = instancesQueryable.Where(x =>
                    (x.Title.Contains(search) || x.Feature.Title.Contains(search))
                    && (x.Feature.FeatureTypeId == null || !notTheseFeatureTypeIds.Contains(x.Feature.FeatureTypeId.Value)));
            }
            else
            {
                instancesQueryable = instancesQueryable.Where(x =>
                    (x.Feature.FeatureTypeId == null || !notTheseFeatureTypeIds.Contains(x.Feature.FeatureTypeId.Value)));
            }

            var excludedOrgProdStatusList = new List<string>() { "Pending", "Suspended", "Deleted" };

            if (orgId != null)
            {
                var productOrgUserRolesIQueryable = _db.ProductOrganizationUsers
                    .Include(x => x.ProductOrganization)
                    .Where(x => x.UserId == userId && !excludedOrgProdStatusList.Contains(x.Status) && x.ProductOrganization.OrganizationId == orgId)
                    .AsQueryable();

                var networkOrgs = _db.NetworkOrganization.Where(x => productOrgUserRolesIQueryable.Any(y => y.ProductOrganization.OrganizationId == x.OrganizationId));

                instancesQueryable = instancesQueryable.Where(
                   y => y.IsDefault != true &&
                   (
                       y.Status == "public"
                       || (y.Status == "organization" && productOrgUserRolesIQueryable.Any(z => z.ProductOrganization.OrganizationId == y.OrganizationId))
                       || (y.Status == "network" && networkOrgs.Any(z => z.OrganizationId == y.OrganizationId))
                       || y.UserId == userId
                   )).Distinct();
            }
            else if (userId != null)
            {
                var productOrgUserRolesIQueryable = _db.ProductOrganizationUsers
                    .Include(x => x.ProductOrganization)
                    .Where(x => x.UserId == userId && !excludedOrgProdStatusList.Contains(x.Status))
                    .AsQueryable();

                var networkOrgs = _db.NetworkOrganization.Where(x => productOrgUserRolesIQueryable.Any(y => y.ProductOrganization.OrganizationId == x.OrganizationId));

                instancesQueryable = instancesQueryable.Where(
                   y => y.IsDefault != true &&
                   (
                       y.Status == "public"
                       || (y.Status == "organization" && productOrgUserRolesIQueryable.Any(z => z.ProductOrganization.OrganizationId == y.OrganizationId))
                       || (y.Status == "network" && networkOrgs.Any(z => z.OrganizationId == y.OrganizationId))
                       || y.UserId == userId
                   )).Distinct();
            }

            var instances = await instancesQueryable
                .OrderBy(x => x.Feature.Title)
                .ThenBy(x => x.Title)
                .Take(20)
                .AsSplitQuery()
                .ToListAsync();

            return instances.Select(x => new Instance(x, userId));
        }

        public async Task<IEnumerable<Instance>> GetInstances(string? query, Guid? organizationId)
        {
            var instancesIQueryable = _db.Instances
                .OrderBy(x => x.Title)
                .AsQueryable();

            if (string.IsNullOrEmpty(query) == false)
            {
                instancesIQueryable = instancesIQueryable.Where(x => x.Title.Contains(query) || x.Feature.Title.Contains(query) || x.Feature.Code.Contains(query));
            }

            if (organizationId != null)
            {
                instancesIQueryable = instancesIQueryable
                    .Where(x => x.OrganizationId == organizationId);
            }

            var instances = await instancesIQueryable.ToListAsync();

            return instances.Select(x => new Instance(x));
        }

        public async Task<MyInstancesResult> GetMyInstances(string? query, Guid? typeId, Guid? userId, Guid? instanceId, CancellationToken token)
        {
            if (userId == null)
            {
                return null;
            }

            var featureTypeIQueryable = _db.Features
                .Include(x => x.FeatureType)
                .Where(x => x.FeatureTypeId != null);

            if (typeId == null)
            {
                featureTypeIQueryable = featureTypeIQueryable.Where(x => x.FeatureType.Name == "Modifiable Learning Container Pages");
            }
            else
            {
                featureTypeIQueryable = featureTypeIQueryable.Where(x => x.FeatureTypeId == typeId);
            }

            //GetFeatureType
            var featureType = await featureTypeIQueryable
                .Select(x => x.FeatureType)
                .FirstOrDefaultAsync(token);

            if (featureType == null)
            {
                return null;
            }

            var instanceSplit = _db.Instances
               .Include(x => x.Feature)
               .ThenInclude(x => x.FeatureType)
               .AsSplitQuery();

            if (featureType.Name == "Portfolio")
            {
                //OnlyOnePortfolioPerUser.
                await CheckUserFeatureInstanceExists(userId, "Portfolio", token);

                var instanceIQueryable = instanceSplit
                    .Where(x => x.UserId == userId && x.Feature.FeatureType.Name == "Portfolio")
                    .AsSplitQuery();

                if (!string.IsNullOrEmpty(query))
                {
                    instanceIQueryable = instanceIQueryable.Where(x => x.Title.ToLower().Contains(query.ToLower()));
                }

                var instances = await instanceIQueryable.ToListAsync(token);

                return new MyInstancesResult(instances.Select(x => new Instance(x, userId)), null);
            }
            else
            {
                //For Classes - Instances
                var instanceIQueryable = instanceSplit
                    .Include(x => x.InstanceUsers)
                        .ThenInclude(x => x.Role)
                    .Where(x => (x.InstanceUsers.Any(y => y.UserId == userId && y.Role.Name.ToLower() == "instructor" && y.Status == "Active") || x.CreatedBy == userId || x.UserId == userId)
                                && x.Feature.FeatureType.Name == "Modifiable Learning Container Pages" && x.Status.ToLower() != "deleted")
                    .AsSplitQuery();

                if (!string.IsNullOrEmpty(query))
                {
                    instanceIQueryable = instanceIQueryable.Where(x => x.Title.ToLower().Contains(query.ToLower()));
                }

                var lastModifiedInstances = await instanceIQueryable
                    .OrderByDescending(x => x.LastModifiedDate)
                    .Take(3)
                    .ToListAsync(token);

                var lastModifiedIds = lastModifiedInstances.Select(x => x.Id).ToList();

                var instances = await instanceIQueryable
                    .Where(x => !lastModifiedIds.Contains(x.Id))
                    .OrderBy(x => x.Id)
                    .Take(20)
                    .ToListAsync(token);

                if (instanceId != null)
                {
                    var instanceSplitQuery = instanceIQueryable
                         .Include(x => x.InstanceSections)
                            .ThenInclude(x => x.InstanceSectionComponents)
                                .ThenInclude(x => x.Component)
                                    .ThenInclude(x => x.ComponentType)
                         .Where(x => x.Id == instanceId && x.InstanceSections.Any(y => y.InstanceSectionComponents.Any(z => z.Component.ComponentType.Name == "Row Manager")))
                         .AsSplitQuery();

                    var instance = await instanceSplitQuery
                        .FirstOrDefaultAsync(token);

                    if (instance != null)
                    {
                        instances.Add(instance);
                    }
                }

                return new MyInstancesResult(instances.Select(x => new Instance(x, userId)), lastModifiedInstances.Select(x => new Instance(x, userId)));
            }
        }

        public IQueryable<Components> GetComponentsIQueryable(Guid? instanceId, bool? isViewField)
        {
            var componentsIQueryable = _db.Components
              .Include(x => x.InstanceSectionComponents)
                .ThenInclude(x => x.InstanceSection)
              .Include(x => x.InstanceSectionComponents)
                .ThenInclude(x => x.InstanceSectionComponentRows)
              .Include(x => x.ComponentType)
              .Include(x => x.TemplateFields)
                  .ThenInclude(x => x.ParentIdSystemPropertyLinkNavigation)
                      .ThenInclude(x => x.Type)
              .Include(x => x.TemplateFields)
                          .ThenInclude(x => x.SystemProperty)
                              .ThenInclude(x => x.Type)
              .Include(x => x.TemplateFields)
                  .ThenInclude(x => x.DropDownLinkType)
              .Include(x => x.TemplateFields)
                  .ThenInclude(x => x.CommunicationBlock)
              .Include(x => x.Section)
              .Include(x => x.InstanceTags.Where(ic => ic.InstanceId == instanceId))
                  .ThenInclude(x => x.Tag)
              .Include(x => x.Assessments)
              .Where(x => x.InstanceSectionComponents.Any(y => y.InstanceSection.InstanceId == instanceId))
              .AsSplitQuery();

            if (isViewField == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => x.TemplateFields.IsViewField == true).OrderBy(x => x.InstanceSortOrder ?? 0);
            }

            return componentsIQueryable;
        }

        public async Task<List<InstanceRowLite>> GetInstanceSectionComponentValue(Guid instanceId, Guid componentId)
        {
            var result = new List<InstanceRowLite>();
            var instanceSectionComponent = await _db.InstanceSectionComponents
                .Include(x => x.Component)
                .ThenInclude(x => x.ComponentType)
                .Where(x => x.InstanceSection.InstanceId == instanceId && x.ComponentId == componentId).FirstOrDefaultAsync();
            if (instanceSectionComponent == null)
            {
                return result;
            }

            if (instanceSectionComponent.Component.ComponentType.Name.ToLower() == "row manager")
            {
                result = await _db.InstanceSectionComponentRows.Where(x => x.InstanceSectionComponentId == instanceSectionComponent.Id).Select(x => new InstanceRowLite
                {
                    Id = x.RowId,
                    SortOrder = x.SortOrder
                }).ToListAsync();
            }
            else
            {
                result = instanceSectionComponent.Value.IndexOf("[{") > 0 ? instanceSectionComponent.Value.Deserialize<List<InstanceRowLite>>() : instanceSectionComponent.Value.Split(";").Select(x => new InstanceRowLite() { Id = x.ToGUID(), SortOrder = 0 }).ToList();//  JsonSerializer.Deserialize<List<InstanceRowLite>>(instanceComponentValue);
            }

            return result;
        }

        public async Task<IEnumerable<RowLite?>> GetInstanceRowManagerComponentRows(Guid? userId, Guid instanceId, CancellationToken token)
        {
            var instance = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureType)
                .Where(x => x.Id == instanceId)
                .FirstOrDefaultAsync(token);

            if (instance == null && instance?.Feature == null)
            {
                return null;
            }

            if (instance.Feature.FeatureType.Name == "Portfolio")
            {
                await CheckForRowManager(instance.Feature, instanceId, (Guid)userId, token);
            }

            var rowIds = await _db.InstanceSectionComponentRows
                .Where(x => x.InstanceSectionComponent.InstanceSection.InstanceId == instanceId
                && x.InstanceSectionComponent.InstanceSection.IsDeleted != true
                && x.InstanceSectionComponent.Component.ComponentType.Name == "Row Manager"
                && x.InstanceSectionComponent.IsDeleted != true)
                .Select(x => x.RowId)
                .ToListAsync(token);

            var rows = await _db.Rows.Where(x => rowIds.Contains(x.Id)).Select(x => new RowLite(x.Id, x.Title)).ToListAsync(token);
            return rows;
        }

        public async Task<Instances?> UpdateInstance(Guid? userId, Guid id, InstanceIn instance)
        {
            var existingInstance = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureType)
                .Include(x => x.InstanceTags)
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync();

            if (existingInstance != null)
            {
                var currentDate = DateTime.UtcNow;

                existingInstance.Title = instance.Title;
                existingInstance.Description = instance.Description;
                existingInstance.IconAssetId = instance.IconAssetId;
                existingInstance.CoverMediaAssetId = instance.CoverMediaAssetId;
                existingInstance.ActionUrl = instance.ActionUrl;
                existingInstance.LastModifiedBy = userId;
                existingInstance.LastModifiedDate = currentDate;
                existingInstance.Status = instance.Status;
                if (instance.DueDate != null)
                {
                    existingInstance.DueDate = instance.DueDate.FromEpoch();
                }

                if (instance.GradeId != null && instance.GradeId != Guid.Empty && existingInstance.Feature.FeatureType.Name == "Modifiable Learning Container Pages")
                {
                    var gradeTagId = await _db.Tags.Where(x => x.Name == "Grade" && x.ParentId == null).Select(x => x.Id).FirstOrDefaultAsync();

                    var instanceTags = await _db.InstanceTags.Where(x => x.Tag.ParentId == gradeTagId).ToListAsync();

                    if (instanceTags != null && instanceTags.Any())
                    {
                        _db.InstanceTags.RemoveRange(instanceTags);
                    }

                    _db.InstanceTags.Add(new InstanceTags() { Id = Guid.NewGuid(), ComponentId = null, InstanceId = existingInstance.Id, TagId = (Guid)instance.GradeId });
                }

                if (instance.EducatorId != null && instance.EducatorId != Guid.Empty)
                {
                    existingInstance.EducatorId = instance.EducatorId;
                    var existingInstanceUserIds = await _db.InstanceUsers.Where(x => x.InstanceId == existingInstance.Id).Select(x => x.UserId).ToListAsync();

                    if (!existingInstanceUserIds.Contains((Guid)instance.EducatorId))
                    {
                        var eductatorRoleId = await _db.Roles.Where(x => x.Name == "Instructor").Select(x => x.Id).FirstOrDefaultAsync();
                        _db.InstanceUsers.Add(new InstanceUsers() { Id = Guid.NewGuid(), InstanceId = existingInstance.Id, Status = "Active", UserId = (Guid)instance.EducatorId, RoleId = eductatorRoleId });
                    }
                }

                await _db.SaveChangesAsync();

                return existingInstance;
            }

            return null;
        }

        public async Task<Instance?> UpdateInstanceStatus(Guid id, string status, Guid userId, DateTime? dueDate, CancellationToken token)
        {
            var existingInstance = await _db.Instances
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync(token);

            if (existingInstance != null)
            {
                existingInstance.Status = status;
                existingInstance.LastModifiedDate = DateTime.UtcNow;
                existingInstance.LastModifiedBy = userId;

                if (dueDate != null)
                {
                    existingInstance.DueDate = dueDate;
                }

                await _db.SaveChangesAsync(token);

                return new Instance(existingInstance);
            }

            return null;
        }

        public async Task<Guid> GetFeatureIdByFeatureTypeId(Guid id)
        {
            var featureId = await _db.Features.Where(x => x.FeatureTypeId == id).Select(x => x.Id).FirstOrDefaultAsync();

            return featureId;
        }
        public async Task<RepositoryBuilderDashboard> CreateInstanceByFeatureId(Guid userId, Guid orgId, Guid featureId, CancellationToken token)
        {
            var feature = await _db.Features.Where(x => x.Id == featureId).FirstOrDefaultAsync(token);

            if (feature == null)
            {
                return null;
            }

            Instance instance = new Instance(feature.Title + new Random().Next(1000, 9999), feature.Id, orgId);

            var result = await CreateInstance(userId, instance, token);

            return result;
        }

        public async Task<RepositoryBuilderDashboard> CreateInstance(Guid userId, Instance instance, CancellationToken token)
        {
            var createdInstance = await PersistInstance(userId, instance);

            if (createdInstance == null)
            {
                return null;
            }

            var feature = await _db.Features
                .Include(x => x.FeatureType)
                .Where(x => x.Id == createdInstance.FeatureId)
                .FirstOrDefaultAsync(token);

            if (createdInstance != null)
            {
                return new RepositoryBuilderDashboard()
                {
                    Id = createdInstance.Id,
                    ObjectName = createdInstance.Title,
                    ShortDescription = createdInstance.Description
                };
            }
            else
            {
                return new RepositoryBuilderDashboard();
            }
        }

        public async Task<bool> CreateInstances(Guid userId, Instance[] instances)
        {
            foreach (var instance in instances)
            {
                await PersistInstance(userId, instance);
            }

            return true;
        }

        private async Task<Instances?> PersistInstance(Guid? userId, Instance instanceIn)
        {
            var joinCode = "I".RandomString(5);

            var existingJoinCodes = await _db.Instances.Select(x => x.JoinCode).ToListAsync();

            var joinCodeExists = existingJoinCodes.Contains(joinCode);

            while (joinCodeExists)
            {
                joinCode = "I".RandomString(5);
                joinCodeExists = existingJoinCodes.Contains(joinCode);
            }

            //Don't allow instance of a feature with the same name
            var existingInstance = await _db.Instances
                .Where(x => x.FeatureId == instanceIn.FeatureId && x.Title == instanceIn.Title + "-" + joinCode)
                .FirstOrDefaultAsync();

            var isJoinCodeInstance = await _db.ProductJoinCodeSettings
                .Include(x => x.ProductOrganization)
                    .ThenInclude(x => x.Product)
                        .ThenInclude(x => x.ProductFeatures)
                .Where(x => x.ProductOrganization.Product.ProductFeatures.Select(y => y.FeatureId).Contains((Guid)instanceIn.FeatureId) && x.TypeName == "Class" && x.IsActive == true)
                .AnyAsync();

            var feature = await _db.Features
                .Include(x => x.FeatureType)
                .Include(x => x.FeatureTabs)
                    .ThenInclude(x => x.Tab)
                        .ThenInclude(x => x.Template)
                .Where(x => x.Id == instanceIn.FeatureId).FirstOrDefaultAsync();

            var templateIds = feature.FeatureTabs.Select(x => x.Tab.TemplateId).ToList();

            var sections = await _db.Sections
                .Include(x => x.Type)
                .Include(x => x.Components)
                .Where(x => templateIds.Contains(x.TemplateId) && x.IsDeleted != true).ToListAsync();

            if (existingInstance == null && instanceIn.FeatureId.HasValue)
            {
                var currentDate = DateTime.UtcNow;

                var newInstance = new Instances()
                {
                    Id = Guid.NewGuid(),
                    FeatureId = instanceIn.FeatureId.Value,
                    Title = feature.FeatureType.Name == "Accredited Learning Container Pages" || feature.FeatureType.Name == "Modifiable Learning Container Pages" ? instanceIn.Title ?? " New Assignment" : instanceIn.Title + "-" + joinCode,
                    Description = instanceIn.Description,
                    IsDefault = instanceIn.IsDefault,
                    OrganizationId = instanceIn.OrganizationId,
                    Grade = instanceIn.GradeId,
                    Section = instanceIn.Section,
                    Subject = instanceIn.Subject,
                    Term = instanceIn.Term,
                    UserId = userId,
                    CreatedBy = userId,
                    CreatedDate = currentDate,
                    LastModifiedBy = userId,
                    LastModifiedDate = currentDate,
                    Status = instanceIn.Status ?? "private",
                    IsJoinCodeInstance = isJoinCodeInstance && feature.FeatureType.Name == "Modifiable Learning Container Pages",
                    JoinCode = joinCode,
                    Slug = joinCode,
                };

                if (instanceIn.CoverMediaAssetId != null)
                {
                    newInstance.CoverMediaAssetId = instanceIn.CoverMediaAssetId;
                }

                if (instanceIn.IconAssetId != null)
                {
                    newInstance.IconAssetId = instanceIn.IconAssetId;
                }

                if (instanceIn.DueDate != null)
                {
                    newInstance.DueDate = instanceIn.DueDate.FromEpoch();
                }

                if (feature.FeatureType.Name == "Modifiable Learning Container Pages" && instanceIn.Title.Length == 0)
                {
                    newInstance.Title = "My Class Name" + "-" + "(" + joinCode + ")";
                }
                ;

                if (instanceIn.EducatorId != null && instanceIn.EducatorId != Guid.Empty)
                {
                    newInstance.EducatorId = instanceIn.EducatorId;
                    var eductatorRoleId = await _db.Roles.Where(x => x.Name == "Instructor").Select(x => x.Id).FirstOrDefaultAsync();
                    _db.InstanceUsers.Add(new InstanceUsers() { Id = Guid.NewGuid(), InstanceId = newInstance.Id, Status = "Active", UserId = (Guid)instanceIn.EducatorId, RoleId = eductatorRoleId });
                }
                else
                {
                    newInstance.EducatorId = userId;
                    var eductatorRoleId = await _db.Roles.Where(x => x.Name == "Instructor").Select(x => x.Id).FirstOrDefaultAsync();
                    _db.InstanceUsers.Add(new InstanceUsers() { Id = Guid.NewGuid(), InstanceId = newInstance.Id, Status = "Active", UserId = (Guid)userId, RoleId = eductatorRoleId });
                }

                _db.Instances.Add(newInstance);

                if (instanceIn.GradeId != null)
                {
                    _db.InstanceTags.Add(new InstanceTags()
                    {
                        Id = Guid.NewGuid(),
                        TagId = instanceIn.GradeId.Value,
                        InstanceId = newInstance.Id
                    });
                }

                if (sections != null && sections.Any())
                {
                    foreach (var section in sections)
                    {
                        var newInstanceSection = new DB.Models.InstanceSections
                        {
                            Id = Guid.NewGuid(),
                            SectionId = section.Id,
                            InstanceId = newInstance.Id,
                            SortOrder = section.SortOrder,
                            TemplateId = section.TemplateId,
                            IsDeleted = false
                        };

                        _db.InstanceSections.Add(newInstanceSection);

                        if (section?.Type?.TypeBw != (int)SectionTypes.Dynamic)
                        {
                            var newInstanceSectionComponents = section.Components.Select(y => new DB.Models.InstanceSectionComponents
                            {
                                Id = Guid.NewGuid(),
                                InstanceSectionId = newInstanceSection.Id,
                                ComponentId = y.Id,
                                Value = null,
                                IsDeleted = false
                            });

                            _db.InstanceSectionComponents.AddRange(newInstanceSectionComponents);
                        }
                    }
                }

                await _db.SaveChangesAsync();

                return newInstance;
            }

            return null;
        }

        public async Task<bool> DeleteInstance(Guid? userId, Guid id)
        {
            var existingInstance = await _db.Instances.Where(x => x.Id == id).FirstOrDefaultAsync();

            if (existingInstance == null)
            {
                return false;
            }

            _db.Instances.Remove(existingInstance);

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<Guid?> GetDefaultAssetId(Guid id, string systemProperty)
        {
            try
            {
                var instance = await _db.Instances
                    .Where(x => x.Id == id)
                    .FirstOrDefaultAsync();

                if (instance == null)
                {
                    return null;
                }

                var componentsIQueryable = GetComponentsIQueryable(id, false);

                var value = await componentsIQueryable.Where(x => x.TemplateFields.SystemProperty.Property == systemProperty).Select(x => x.TemplateFields.DefaultImageUrl).FirstOrDefaultAsync();

                if (Guid.TryParse(value, out var assetId))
                {
                    return assetId;
                }
            }
            catch (Exception)
            {
                return null;
            }
            return null;
        }

        public async Task<bool> SetInstanceJoinCode(Guid? userId, Guid instanceId, bool isJoinCodeInstance)
        {
            var existingInstance = await _db.Instances.Where(x => x.Id == instanceId).FirstOrDefaultAsync();

            if (existingInstance == null)
            {
                return false;
            }

            if (existingInstance.IsJoinCodeInstance != isJoinCodeInstance)
            {
                if (string.IsNullOrEmpty(existingInstance.JoinCode))
                {
                    existingInstance.JoinCode = isJoinCodeInstance ? await GenerateJoinCode("I") : null;
                }
                existingInstance.IsJoinCodeInstance = isJoinCodeInstance;
            }

            await _db.SaveChangesAsync();

            return true;
        }

        private async Task<Instances> SetDefaultAsset(Instances instance)
        {

            var defaultInstance = await _db.Instances.Include(x => x.Feature).Where(x => x.FeatureId == instance.FeatureId && x.IsDefault == true).FirstOrDefaultAsync();
            if (defaultInstance != null)
            {
                if (instance.IconAssetId == null)
                {
                    instance.IconAssetId = defaultInstance.IconAssetId ?? defaultInstance.Feature.IconAssetId;
                }
                if (instance.CoverMediaAssetId == null)
                {
                    instance.CoverMediaAssetId = defaultInstance.CoverMediaAssetId ?? defaultInstance.Feature.CoverMediaAssetId;
                }
            }

            return instance;
        }

        private async Task CheckUserFeatureInstanceExists(Guid? userId, string featureTypeName, CancellationToken token)
        {
            var instance = await _db.Instances
                .Where(x => x.Feature.FeatureType.Name == featureTypeName && x.UserId == userId)
                .FirstOrDefaultAsync(token);

            if (instance == null)
            {
                await AddUserInstance(userId, featureTypeName, token);
            }
        }

        private async Task AddUserInstance(Guid? userId, string featureTypeName, CancellationToken token)
        {
            var feature = await _db.Features
                .Include(x => x.FeatureType)
                .Include(x => x.FeatureTabs)
                    .ThenInclude(x => x.Tab)
                        .ThenInclude(x => x.Template)
                            .ThenInclude(x => x.Sections)
                                .ThenInclude(x => x.Components)
                .Where(x => x.FeatureType.Name == featureTypeName)
                .FirstOrDefaultAsync(token);

            var defaultOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor Inc.").Select(x => x.Id).FirstOrDefaultAsync(token);

            var currentDate = DateTime.UtcNow;

            var newInstance = new Instances()
            {
                Id = Guid.NewGuid(),
                FeatureId = feature.Id,
                Title = feature.Title,
                Description = feature.Description,
                IsDefault = true,
                OrganizationId = defaultOrgId,
                UserId = userId,
                CreatedBy = userId,
                CreatedDate = currentDate,
                LastModifiedBy = userId,
                LastModifiedDate = currentDate
            };

            var sections = feature.FeatureTabs.SelectMany(x => x.Tab.Template.Sections).ToList();

            var sectionsToAdd = sections.Select(x => new InstanceSections()
            {
                Id = Guid.NewGuid(),
                InstanceId = newInstance.Id,
                SortOrder = x.SortOrder,
                TemplateId = x.TemplateId,
                SectionId = x.Id,
                IsDeleted = false
            });

            _db.AddRange(sectionsToAdd);

            //TODO: Add InstanceSectionComponents? This is never added
            // foreach (var section in sections)
            // {
            //     var componentsToAdd = section.Components.Select(x => new InstanceSectionComponents()
            //     {
            //         Id = Guid.NewGuid(),
            //         InstanceSectionId = sectionsToAdd.Where(x => x.SectionId == section.Id).FirstOrDefault().Id,
            //         ComponentId = x.Id,
            //         Value = "",
            //         IsDeleted = false
            //     });
            // }

            _db.Instances.Add(newInstance);

            await _db.SaveChangesAsync(token);

            if (userId != null)
            {
                await CheckForRowManager(feature, newInstance.Id, (Guid)userId, token);
            }
        }

        private async Task AddUserToInstance(Guid userId, Guid? organizationId, Guid instanceId)
        {
            var orgUser = await _db.OrganizationUsers
                .Where(x => x.UserId == userId && x.OrganizationId == organizationId && x.Status != "Deleted")
                .FirstOrDefaultAsync();

            var instanceUser = await _db.InstanceUsers
                .Where(x => x.InstanceId == instanceId && x.UserId == userId)
                .FirstOrDefaultAsync();

            if (orgUser != null && instanceUser == null)
            {
                _db.InstanceUsers.Add(new InstanceUsers()
                {
                    Id = Guid.NewGuid(),
                    InstanceId = instanceId,
                    RoleId = orgUser.RoleId,
                    UserId = userId,
                    Status = "Active"
                });

                await _db.SaveChangesAsync();
            }
        }

        private async Task CheckForRowManager(Features feature, Guid instanceId, Guid userId, CancellationToken token)
        {
            var instanceSectionIds = await _db.InstanceSections.Where(x => x.InstanceId == instanceId && x.IsDeleted != true).Select(x => x.Id).ToListAsync(token);

            var instanceSectionComponentsIQueryable = GetInstanceSectionComponentsIQueryable(instanceSectionIds, instanceId, true);

            var instanceSectionComponent = await instanceSectionComponentsIQueryable
                .Where(x => x.InstanceSection.InstanceId == instanceId
                    && x.IsDeleted == false
                    && x.InstanceSection.IsDeleted != true
                    && x.Component.ComponentType.Name == "Row Manager")
                .FirstOrDefaultAsync(token);

            //CheckRowManagerComponent.
            if (instanceSectionComponent == null)
            {
                var instance = await _db.Instances
                    .Include(x => x.Feature)
                        .ThenInclude(x => x.FeatureTabs)
                            .ThenInclude(x => x.Tab)
                    .Where(x => x.Id == instanceId)
                    .FirstOrDefaultAsync(token);

                if (instance != null)
                {
                    var featureTab = instance.Feature.FeatureTabs
                        .Where(x => x.Tab.Name == "DASHBOARD")
                        .FirstOrDefault();

                    if (featureTab != null)
                    {
                        //GetTemplate.
                        var template = await _db.Templates
                            .Include(x => x.Sections)
                                .ThenInclude(x => x.Components)
                                    .ThenInclude(x => x.ComponentType)
                            .Include(x => x.Sections)
                                .ThenInclude(x => x.InstanceSectionsSection)
                                    .ThenInclude(x => x.InstanceSectionComponents)
                                        .ThenInclude(x => x.Component)
                                            .ThenInclude(x => x.ComponentType)
                            .Where(x => x.Id == featureTab.Tab.TemplateId)
                            .FirstOrDefaultAsync(token);

                        if (template != null)
                        {
                            var section = template.Sections
                                .Where(x => x.Components.Any(y => y.ComponentType.Name == "Row Manager") && x.IsDeleted != true)
                                .FirstOrDefault();

                            if (section == null)
                            {
                                var sectionType = await _db.SectionTypes
                                    .Where(x => x.TypeBw == 1)
                                    .FirstOrDefaultAsync(token);

                                section = new Sections()
                                {
                                    Id = Guid.NewGuid(),
                                    TemplateId = featureTab.Tab.TemplateId,
                                    Title = "",
                                    Description = "",
                                    LayoutType = 0,
                                    SortOrder = 0,
                                    Expandable = null,
                                    HideBackground = true,
                                    TypeId = sectionType.Id,
                                    IsDeleted = false,
                                    ShowOnInstanceViewer = false,
                                    ShowTitleOnPlayer = false,
                                    ShowDescOnPlayer = false,
                                    IsEditable = false,
                                    Caption = null,
                                    ShowOnPlayerSidepanel = true
                                };

                                _db.Sections.Add(section);
                            }

                            var instanceSectionIn = new InstanceSections()
                            {
                                Id = Guid.NewGuid(),
                                TemplateId = featureTab.Tab.TemplateId,
                                IsDeleted = false,
                                InstanceId = instanceId,
                                SectionId = section.Id,
                                SortOrder = 0,
                                Title = feature.Title
                            };

                            _db.InstanceSections.Add(instanceSectionIn);

                            var component = template.Sections
                            .Where(x => x.IsDeleted != true)
                            .SelectMany(x => x.Components.Where(y => y.ComponentType.Name == "Row Manager"))
                            .FirstOrDefault();

                            if (component != null)
                            {
                                var newInstanceSectionComponent = new InstanceSectionComponents()
                                {
                                    Id = Guid.NewGuid(),
                                    InstanceSectionId = instanceSectionIn.Id,
                                    ComponentId = component.Id,
                                    Value = null,
                                    IsDeleted = false
                                };

                                _db.InstanceSectionComponents.Add(newInstanceSectionComponent);
                            }

                            await _db.SaveChangesAsync(token);
                        }
                    }
                }
            }

            instanceSectionComponent = await instanceSectionComponentsIQueryable
                .Where(x => x.InstanceSection.InstanceId == instanceId
                    && x.IsDeleted == false
                    && x.InstanceSection.IsDeleted != true
                    && x.Component.ComponentType.Name == "Row Manager")
                .FirstOrDefaultAsync(token);

            if (instanceSectionComponent != null && (instanceSectionComponent.InstanceSectionComponentRows == null || instanceSectionComponent.InstanceSectionComponentRows.Count == 0))
            {
                var isClassroom = feature.FeatureType.Name == "Modifiable Learning Container Pages";
                var rowId = await AddRowManagerRow(isClassroom == true ? "New Assignment" : feature.Title, instanceId, isClassroom, userId);

                //Must Take In Account CamelCase On Serialize.
                await UpdateInstanceSectionComponent(userId, instanceSectionComponent.Id, new InstanceSectionComponentValue(new List<InstanceRowLite> { new() { Id = rowId, SortOrder = 0 } }.Serialize()), token);
            }
        }

        private async Task<Guid> AddRowManagerRow(string title, Guid instanceId, bool isClassroom, Guid userId)
        {
            var defaultRowDisplay = await _db.RowDisplays.Where(x => x.Name == "Standard").FirstOrDefaultAsync();
            var defaultRowType = await _db.RowTypes.Where(x => x.Name == "Manual").FirstOrDefaultAsync();
            var classroomRowType = await _db.RowTypes.Where(x => x.Name == "Modifiable Package").FirstOrDefaultAsync();

            var defaultThumbNaileType = await _db.ThumbnailTypes.Where(x => x.Name == "Landscape" && x.RowTypeId == defaultRowType.Id).FirstOrDefaultAsync();
            var defaultTitleHtmlTag = "h1";
            var defaultDescriptionHtmlTag = "p";
            var defaultAlignment = "left";

            var instance = await _db.Instances.Where(x => x.Id == instanceId).FirstOrDefaultAsync();
            instance.LastModifiedDate = DateTime.UtcNow;
            instance.LastModifiedBy = userId;

            var r = new Rows()
            {
                Id = Guid.NewGuid(),
                Title = title,
                TitleHtmltag = defaultTitleHtmlTag,
                Description = "",
                DescriptionHtmltag = defaultDescriptionHtmlTag,
                RowTypeId = isClassroom == true ? classroomRowType.Id : defaultRowType.Id,
                Alignment = defaultAlignment,
                CourouselFlag = false,
                ThumbnailTypeId = defaultThumbNaileType.Id,
                DisplayTypeId = defaultRowDisplay.Id,
                ComponentId = null,
                LiveUpdating = false,
                Status = "Published"
            };

            _db.Rows.Add(r);
            await _db.SaveChangesAsync();

            return r.Id;
        }

        public async Task<string> GenerateJoinCode(string startLetter)
        {
            string code = startLetter.RandomString(5);
            switch (startLetter)
            {
                case "I":
                    if (await _db.Instances.AnyAsync(x => x.JoinCode == code))
                    {
                        await GenerateJoinCode(startLetter);
                    }
                    break;
                case "P":
                    if (await _db.ProductOrganizationJoinCodes.AnyAsync(x => x.JoinCode == code))
                    {
                        await GenerateJoinCode(startLetter);
                    }
                    break;
                case "O":
                    if (await _db.Organizations.AnyAsync(x => x.JoinCode == code))
                    {
                        await GenerateJoinCode(startLetter);
                    }
                    break;
            }

            return code;
        }

        public async Task<Guid?> GetPrimaryEductator(Guid? instanceId)
        {
            var educatorComponent = Guid.Parse("994E678A-37E0-48EA-84D6-C8A41C1B87F8");
            var m = await _db.InstanceSectionComponents.Where(x => x.ComponentId == educatorComponent && x.InstanceSection.InstanceId == instanceId).FirstOrDefaultAsync();
            if (m == null || m.Value == null)
            {
                return null;
            }

            return Guid.Parse(m.Value);
        }

        public async Task<Instance> GetById(Guid id)
        {
            var m = await _db.Instances.Where(x => x.Id == id).FirstOrDefaultAsync();
            return new Instance(m);
        }

        public async Task<Guid?> GetInstanceOwner(Guid? id)
        {
            var m = await _db.Instances.Where(x => x.Id == id).Select(x => x.UserId).FirstOrDefaultAsync();
            return m;
        }

        private IQueryable<InstanceSections> GetInstanceSectionsIQueryable(Guid instanceId)
        {
            return _db.InstanceSections
                      .Include(x => x.Section)
                      .ThenInclude(x => x.Type)
                      .Include(x => x.Section)
                      .ThenInclude(x => x.SectionActions)
                      .ThenInclude(x => x.Action)
                      .Where(x => x.InstanceId == instanceId)
                      .AsNoTrackingWithIdentityResolution()
                      .AsSplitQuery();
        }

        public IQueryable<InstanceSectionComponents> GetInstanceSectionComponentsIQueryable(IEnumerable<Guid> instanceSectionIds, Guid instanceId, bool isViewField = false, bool isHoverField = false, bool isPreviewField = false, bool isRequiredField = false, bool isScorm = false, bool isBuilderField = false)
        {
            var componentsIQueryable = _db.InstanceSectionComponents
                    .Include(x => x.Component)
                        .ThenInclude(x => x.ComponentType)
                            .ThenInclude(x => x.Parent)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.Rows)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.TemplateFields)
                            .ThenInclude(x => x.SystemProperty)
                                .ThenInclude(x => x.Type)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.TemplateFields)
                            .ThenInclude(x => x.ParentIdSystemPropertyLinkNavigation)
                                .ThenInclude(x => x.Type)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.TemplateFields)
                            .ThenInclude(x => x.DropDownLinkType)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.TemplateFields)
                            .ThenInclude(x => x.CommunicationBlock)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.TemplateFields)
                    .Include(x => x.InstanceSectionComponentRows)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.Question)
                            .ThenInclude(x => x.QuestionType)
                    .Include(x => x.Component)
                        .ThenInclude(x => x.Question)
                            .ThenInclude(x => x.QuestionAnswers.Where(y => y.InstanceId == instanceId || y.InstanceId == null))
                                .ThenInclude(x => x.Tag)
                    .Where(x => instanceSectionIds.Contains(x.InstanceSectionId))
                    .AsNoTrackingWithIdentityResolution()
                    .AsSplitQuery();

            if (isViewField == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => x.Component.TemplateFields.IsViewField != false);
            }

            if (isHoverField == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => x.Component.TemplateFields.IsHoverField == true);
            }

            if (isPreviewField == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => x.Component.TemplateFields.IsPreviewField == true);
            }

            if (isRequiredField == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => x.Component.TemplateFields.IsRequiredField == true);
            }

            if (isScorm == true)
            {
                componentsIQueryable = componentsIQueryable.Where(x => !x.Component.ComponentType.Name.Contains("Assessment Block") && !x.Component.ComponentType.Name.Contains("Download Block"));
            }

            //SortByRouNumber - SortOrder
            componentsIQueryable = componentsIQueryable
                .OrderBy(x => x.Component.BuilderRowNumber)
                .ThenBy(x => x.Component.BuilderSortOrder);

            return componentsIQueryable.Select(x => new InstanceSectionComponents
            {
                Component = x.Component,
                ComponentId = x.ComponentId,
                Id = x.Id,
                InstanceSection = x.InstanceSection,
                InstanceSectionComponentRows = x.InstanceSectionComponentRows,
                InstanceSectionId = x.InstanceSectionId,
                IsDeleted = x.IsDeleted,
                UserAnswers = x.UserAnswers,
                Value = x.Value
            });
        }

        public async Task<InstanceTemplate> GetInstanceTemplateById(Guid? userId, Guid instanceId, Guid templateId, bool isViewField = false, bool isBuilderField = false, bool isScorm = false)
        {
            var sections = await GetInstanceSectionsIQueryable(instanceId)
                .Where(x => x.TemplateId == templateId && x.IsDeleted != true && x.Section.ShowOnInstanceViewer == true)
                .ToListAsync();

            if (isBuilderField == true)
            {
                sections = sections.Where(x => x.IsHidden != true).ToList();
            }

            if (isScorm == false)
            {
                var efOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor Inc.").Select(x => x.Id).FirstOrDefaultAsync();

                var featureId = await _db.Instances.Where(x => x.Id == instanceId).Select(x => x.FeatureId).FirstOrDefaultAsync();

                var userIsEfAdmin = await _db.OrganizationUsers.Include(x => x.Role).AnyAsync(x => x.UserId == userId && x.OrganizationId == efOrgId && x.Role.Name == "Administrator" && x.Status != "Deleted");

                var productOrgUserRoles = _db.ProductOrganizationUsers
                .Where(x => x.UserId == userId && x.Status != "Deleted");

                var productFeatureRolesIQueryable = _db.ProductFeatureRoles
                        .Include(x => x.Role)
                        .Where(x => x.ProductFeature.FeatureId == featureId &&
                        productOrgUserRoles.Any(p => p.ProductOrganization.ProductId == x.ProductFeature.ProductId && p.RoleId == x.RoleId))
                        .OrderByDescending(x => x.ActionBw)
                        .AsSplitQuery();

                var productFeatureRoles = await productFeatureRolesIQueryable.ToListAsync();

                sections = sections.Where(x => (
                    x.Section.SectionActions.Count == 0 ||
                    x.Section.SectionActions.Any(a =>
                        productFeatureRoles.Any(p => (p.ActionBw & a.Action.ActionBw) > 0))
                || userIsEfAdmin == true)).ToList();
            }

            var instanceSectionComponents = await GetInstanceSectionComponentsIQueryable(sections.Select(x => x.Id),
                    instanceId,
                    isViewField,
                    false,
                    false,
                    false,
                    false,
                    isBuilderField)
                .Where(x => x.IsDeleted != true)
                .ToListAsync();

            var questionIds = instanceSectionComponents.Where(x => x.Component.QuestionId != null).Select(x => x.Component.QuestionId).ToList();
            var userAnswers = await GetUserAnswers(userId, questionIds, instanceId);
            var instanceComponentTagIds = instanceSectionComponents.Where(x => x.Component.ComponentType.Name == "Dropdown" && x.Component.ComponentType.ParentId == null && x.Component.TemplateFields?.DropDownLinkType?.Title == "Tags" && x.Component?.TemplateFields?.IsTag != true).Select(x => x.Value.ToGUIDNull()).ToList();
            var tags = await GetInstanceComponentLinkedTags(instanceComponentTagIds);

            return new InstanceTemplate(templateId,
                sections.Select(x => new InstanceSection(x,
                        instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id)
                            .OrderBy(y => y.Component.BuilderRowNumber)
                            .ThenBy(y => y.Component?.TemplateFields?.ColNumber),
                        userAnswers,
                        tags,
                        isBuilderField))
                .OrderBy(x => x.SortOrder));
        }

        public async Task<List<InstanceSection>> GetInstanceSections(Guid? userId, Guid instanceId, bool isScorm = false)
        {
            var instanceFeatureTabs = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Tab)
                .Where(x => x.Id == instanceId)
                .Select(x => x.Feature.FeatureTabs.Where(y => x.IsDefault == true ? y.IsDefaultInstanceTab == true : y.IsDefaultInstanceTab != true))
                .FirstOrDefaultAsync();

            var instanceTabIds = instanceFeatureTabs.Select(x => x.TabId).ToList();

            var instanceSections = await GetInstanceSectionsIQueryable(instanceId)
                .Include(x => x.InstanceSectionComponents)
                    .ThenInclude(x => x.Component)
                        .ThenInclude(x => x.Assessments)
                            .ThenInclude(x => x.UserAssessmentFeedback)
                .Include(x => x.Template)
                    .ThenInclude(x => x.Tabs)
                .Where(x => x.IsDeleted != true && x.IsHidden != true && x.InstanceSectionComponents.Any(y => y.Component.TemplateFields.IsViewField == true) && x.Section.Type.Title != "Authoring" && x.Template.Tabs.Any(y => instanceTabIds.Contains(y.Id)))
                .ToListAsync();

            if (isScorm == false)
            {
                var efOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor Inc.").Select(x => x.Id).FirstOrDefaultAsync();

                var featureId = await _db.Instances.Where(x => x.Id == instanceId).Select(x => x.FeatureId).FirstOrDefaultAsync();

                var userIsEfAdmin = await _db.OrganizationUsers.Include(x => x.Role).AnyAsync(x => x.UserId == userId && x.OrganizationId == efOrgId && x.Role.Name == "Administrator" && x.Status != "Deleted");

                var productOrgUserRoles = _db.ProductOrganizationUsers
                .Where(x => x.UserId == userId && x.Status != "Deleted");

                var productFeatureRolesIQueryable = _db.ProductFeatureRoles
                        .Include(x => x.Role)
                        .Where(x => x.ProductFeature.FeatureId == featureId &&
                        productOrgUserRoles.Any(p => p.ProductOrganization.ProductId == x.ProductFeature.ProductId && p.RoleId == x.RoleId))
                        .OrderByDescending(x => x.ActionBw);

                var productFeatureRoles = await productFeatureRolesIQueryable.ToListAsync();

                instanceSections = instanceSections.Where(x => (
                    x.Section.SectionActions.Count == 0 ||
                    x.Section.SectionActions.Any(a =>
                        productFeatureRoles.Any(p => (p.ActionBw & a.Action.ActionBw) > 0))
                || userIsEfAdmin == true)).ToList();
            }

            return instanceSections.Select(x => new InstanceSection(x, null, instanceFeatureTabs.Where(y => y.Tab.TemplateId == x.TemplateId).Select(y => y.SortOrder).FirstOrDefault(),
                x.InstanceSectionComponents.Any(y => y.Component.Assessments.Any()),
                x.InstanceSectionComponents.Any(y => y.Component.Assessments.Any(z => z.UserAssessmentFeedback.Where(a => a.UserId == userId && a.IsGraded == true).Any()))))
                .OrderBy(x => x.TabSortOrder)
                .ThenBy(x => x.SortOrder).ToList();
        }

        public async Task<bool> CheckInstanceSectionIsGraded(Guid instanceId, Guid instanceSectionId, Guid userId)
        {
            var instanceSection = await GetInstanceSectionsIQueryable(instanceId)
                .Include(x => x.InstanceSectionComponents)
                    .ThenInclude(x => x.Component)
                        .ThenInclude(x => x.Question)
                            .ThenInclude(x => x.UserAnswers.Where(y => y.UserId == userId))
                .Where(x => x.Id == instanceSectionId)
                .AsSplitQuery()
                .AsNoTrackingWithIdentityResolution()
                .FirstOrDefaultAsync();

            var graded = instanceSection.InstanceSectionComponents.Where(x => x.Component.Question != null).Any(x => x.Component.Question.UserAnswers != null && x.Component.Question.UserAnswers.Any(z => z.UserId == userId && (z.IsEducatorGraded == true || z.IsAutoGraded == true)));

            return graded;
        }

        public async Task<Guid> GetInstanceIdBySlug(string slug)
        {
            var instanceId = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Tab).Where(x => (x.Feature.Slug == slug && x.IsDefault == true) || x.Slug == slug)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            return instanceId;
        }

        public async Task<List<UserAnswers>> GetUserAnswers(Guid? userId, List<Guid?> questionIds, Guid? instanceId = null)
        {
            var userAnswersIQueryable = _db.UserAnswers
                .Include(x => x.UserQuestionAnswers)
                .Where(x => x.UserId == userId && questionIds.Contains(x.QuestionId)).AsSplitQuery();

            if (instanceId != null)
            {
                userAnswersIQueryable = userAnswersIQueryable.Where(x => x.InstanceId == instanceId || x.InstanceId == null);
            }

            var userAnswers = await userAnswersIQueryable
                .ToListAsync();

            return userAnswers;
        }

        public async Task<InstanceSectionsAndComponentsResult> GetInstanceSectionsAndComponents(Guid? userId, Guid? instanceId, bool isViewField = false, bool isHoverField = false, bool isPreviewField = false, bool isRequiredField = false, bool isScorm = false)
        {
            var instance = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureTabs)
                        .ThenInclude(x => x.Tab)
                .Where(x => x.Id == instanceId)
                .FirstOrDefaultAsync();

            if (instance == null)
            {
                return null;
            }

            var isDefault = instance.IsDefault;

            var progress = await _db.VwInstanceProgressGrade.Where(x => x.Id == instance.Id && x.UserId == userId).FirstOrDefaultAsync();

            IEnumerable<FeatureTabs> instanceFeatureTabs;

            if (isDefault == true)
            {
                instanceFeatureTabs = instance.Feature.FeatureTabs.Where(x => x.IsDefaultInstanceTab == true && x.IsDeleted != true);
            }
            else
            {
                instanceFeatureTabs = instance.Feature.FeatureTabs.Where(x => x.IsDefaultInstanceTab != true && x.IsDeleted != true);
            }

            var instanceFeatureTabTemplateIds = instanceFeatureTabs.Select(x => x.Tab.TemplateId).ToList();

            var instanceSections = await GetInstanceSectionsIQueryable(instanceId.ToGUID())
                .Where(x =>
                instanceFeatureTabTemplateIds.Contains(x.TemplateId) &&
                x.InstanceSectionComponents.Any(y => y.Component.TemplateFields == null || y.Component.TemplateFields.IsViewField == true) &&
                x.Section.Type.Title != "Authoring" && x.IsDeleted != true && x.IsHidden != true && x.Section.ShowOnInstanceViewer == true)
                .ToListAsync();

            if (isScorm == false)
            {
                var efOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor Inc.").Select(x => x.Id).FirstOrDefaultAsync();

                var userIsEfAdmin = await _db.OrganizationUsers.Include(x => x.Role).AnyAsync(x => x.UserId == userId && x.OrganizationId == efOrgId && x.Role.Name == "Administrator" && x.Status != "Deleted");

                var productOrgUserRoles = _db.ProductOrganizationUsers
                .Where(x => x.UserId == userId && x.Status != "Deleted");

                var productFeatureRolesIQueryable = _db.ProductFeatureRoles
                        .Include(x => x.Role)
                        .Where(x => x.ProductFeature.FeatureId == instance.FeatureId &&
                        productOrgUserRoles.Any(p => p.ProductOrganization.ProductId == x.ProductFeature.ProductId && p.RoleId == x.RoleId))
                        .OrderByDescending(x => x.ActionBw);

                var productFeatureRoles = await productFeatureRolesIQueryable.ToListAsync();

                instanceSections = instanceSections.Where(x => (
                        x.Section.SectionActions.Count == 0 ||
                        x.Section.SectionActions.Any(a =>
                            productFeatureRoles.Any(p => (p.ActionBw & a.Action.ActionBw) > 0))
                    || userIsEfAdmin == true)).ToList();
            }

            var instanceSectionComponentsSplit = GetInstanceSectionComponentsIQueryable(instanceSections.Select(x => x.Id), (Guid)instanceId, isViewField, isHoverField, isPreviewField, isRequiredField, isScorm)
                                                 .Where(x => x.IsDeleted != true)
                                                 .AsSplitQuery();

            var instanceSectionComponents = await instanceSectionComponentsSplit
                                                  .OrderBy(x => x.Component.BuilderRowNumber)
                                                  .ThenBy(x => x.Component.BuilderSortOrder)
                                                  .ToListAsync();

            List<Models.SystemProperties.SystemPropertyValue> systemPropertyValues = new List<Models.SystemProperties.SystemPropertyValue>();

            if (isHoverField == true)
            {
                if (instance != null)
                {
                    var instanceSystemProperties = await _systemPropertyService.GetSystemPropertyValues((int)Enums.SystemPropertyTypes.Instance, instanceId);

                    systemPropertyValues.AddRange(instanceSystemProperties);

                    if (instance.OrganizationId != null)
                    {
                        var orgSystemProperties = await _systemPropertyService.GetSystemPropertyValues((int)Enums.SystemPropertyTypes.Organization, instance.OrganizationId);
                        systemPropertyValues.AddRange(orgSystemProperties);
                    }

                    if (instance.UserId != null)
                    {
                        var userSystemProperties = await _systemPropertyService.GetSystemPropertyValues((int)Enums.SystemPropertyTypes.User, instance.UserId);
                        systemPropertyValues.AddRange(userSystemProperties);
                    }
                }
            }

            var questionIds = instanceSectionComponents.Where(x => x.Component.QuestionId != null).Select(x => x.Component.QuestionId).ToList();
            var userAnswers = await GetUserAnswers(userId, questionIds, instanceId);
            var instanceComponentTagIds = instanceSectionComponents.Where(x => x.Component.ComponentType.Name == "Dropdown" && x.Component.TemplateFields?.DropDownLinkType?.Title == "Tags" && x.Component.TemplateFields?.IsTag != true).Select(x => x.Value.ToGUIDNull()).ToList();
            var tags = await GetInstanceComponentLinkedTags(instanceComponentTagIds);

            List<InstanceSection>? result;

            if (systemPropertyValues.Any())
            {
                result = instanceSections.Select(x => new InstanceSection(x, instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id), systemPropertyValues, instanceFeatureTabs.Where(y => y.Tab.TemplateId == x.TemplateId).Select(y => y.SortOrder).FirstOrDefault(),
                    instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id).Any(z => z.Component.QuestionId != null),
                    instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id).Any(z => z.Component.QuestionId != null && z.Component.Question.UserAnswers.Any(a => a.UserId == userId && a.IsEducatorGraded == true)),
                    userAnswers,
                    tags
                    ))
                .OrderBy(x => x.TabSortOrder)
                .ThenBy(x => x.SortOrder)
                .ToList();
            }
            else
            {

                result = instanceSections.Select(x => new InstanceSection(x, instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id), instanceFeatureTabs.Where(y => y.Tab.TemplateId == x.TemplateId).Select(y => y.SortOrder).FirstOrDefault(),
                    instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id).Any(z => z.Component.QuestionId != null),
                    instanceSectionComponents.Where(y => y.InstanceSectionId == x.Id).Any(z => z.Component.QuestionId != null && z.Component.Question.UserAnswers.Any(a => a.UserId == userId && a.IsEducatorGraded == true)),
                    userAnswers,
                    tags
                    ))
                    .OrderBy(x => x.TabSortOrder)
                    .ThenBy(x => x.SortOrder)
                    .ToList();
            }

            return new InstanceSectionsAndComponentsResult(progress?.Status ?? "", progress?.ContainsGrading == 1 ? true : false, progress?.IsGraded ?? false, result);
        }

        public async Task<InstanceSection> AddInstanceSection(Guid? userId, InstanceSectionIn instanceSectionIn)
        {
            var existingInstanceSection = await _db.InstanceSections
                .Where(x => x.Id == instanceSectionIn.Id)
                .FirstOrDefaultAsync();

            var masterSectionComponentIds = await _db.Components
                    .Include(x => x.TemplateFields)
                .Where(x => x.SectionId == instanceSectionIn.MasterSectionId && x.IsLocked == true)
                .Select(x => x.Id)
                .ToListAsync();

            var dynamicSectionTypeId = await _db.SectionTypes.Where(x => x.TypeBw == (int)SectionTypes.Dynamic).Select(x => x.Id).FirstOrDefaultAsync();

            if (existingInstanceSection != null)
            {
                existingInstanceSection.SortOrder = instanceSectionIn.SortOrder;
            }
            else
            {
                var newSection = new Sections()
                {
                    Id = Guid.NewGuid(),
                    TypeId = dynamicSectionTypeId,
                    IsContinuousFeedback = true,
                    Title = instanceSectionIn.Title,
                    BackgroundColor = instanceSectionIn.BackgroundColor,
                    ShowDescOnPlayer = false,
                    ShowOnPlayerSidepanel = true,
                    ShowTitleOnPlayer = false,
                    ShowOnInstanceViewer = true
                };

                existingInstanceSection = new InstanceSections()
                {
                    Id = Guid.NewGuid(),
                    InstanceId = instanceSectionIn.InstanceId,
                    SortOrder = instanceSectionIn.SortOrder,
                    TemplateId = instanceSectionIn.TemplateId,
                    SectionId = newSection.Id,
                    MasterSectionId = instanceSectionIn.MasterSectionId,
                    Title = instanceSectionIn.Title
                };

                _db.Sections.Add(newSection);
                _db.InstanceSections.Add(existingInstanceSection);
                await _db.SaveChangesAsync();

                if (masterSectionComponentIds != null && masterSectionComponentIds.Any())
                {
                    await AddInstanceSectionComponents(existingInstanceSection.Id, masterSectionComponentIds);
                }
            }


            var instance = await _db.Instances.Where(x => x.Id == instanceSectionIn.InstanceId).FirstOrDefaultAsync();
            instance.LastModifiedDate = DateTime.UtcNow;
            instance.LastModifiedBy = userId;

            await _db.SaveChangesAsync();

            existingInstanceSection = await GetInstanceSectionsIQueryable(instanceSectionIn.InstanceId)
                .Where(x => x.Id == existingInstanceSection.Id)
            .FirstOrDefaultAsync();

            var instanceSectionComponentsSplit = GetInstanceSectionComponentsIQueryable(new List<Guid>() { existingInstanceSection.Id }, existingInstanceSection.InstanceId, true)
                                     .Where(x => x.IsDeleted != true)
                                     .AsSplitQuery();

            var instanceSectionComponents = await instanceSectionComponentsSplit
                                                  .OrderBy(x => x.Component.BuilderRowNumber)
                                                  .ThenBy(x => x.Component.BuilderSortOrder)
                                                  .ToListAsync();

            return new InstanceSection(existingInstanceSection, instanceSectionComponents);
        }

        public async Task<bool> UpdateInstanceSections(Guid? userId, IEnumerable<InstanceSectionIn> instanceSectionsIn, bool updateSortOrder = false)
        {
            if (instanceSectionsIn.Any())
            {
                var instanceSectionIds = instanceSectionsIn.Select(x => x.Id).ToList();
                var instanceSections = await _db.InstanceSections.Where(x => instanceSectionIds.Contains(x.Id)).ToListAsync();

                foreach (var instanceSection in instanceSectionsIn)
                {
                    var existingSection = instanceSections.Where(x => x.Id == instanceSection.Id).FirstOrDefault();

                    if (existingSection != null)
                    {
                        if (updateSortOrder == true)
                        {
                            existingSection.SortOrder = instanceSection.SortOrder;
                        }
                        existingSection.Title = instanceSection.Title;
                        existingSection.IsHidden = instanceSection.IsHidden;
                        existingSection.BackgroundColor = instanceSection.BackgroundColor;
                    }

                    var instance = await _db.Instances.Where(x => x.Id == instanceSection.InstanceId).FirstOrDefaultAsync();
                    instance.LastModifiedDate = DateTime.UtcNow;
                    instance.LastModifiedBy = userId;
                }

                await _db.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<bool> UpdateInstanceSectionComponents(Guid? userId, IEnumerable<InstanceSectionComponentIn> instanceSectionComponentsIn, CancellationToken token)
        {
            if (instanceSectionComponentsIn.Any())
            {
                var instanceSectionComponentIds = instanceSectionComponentsIn.Select(x => x.Id).ToList();
                var instanceSectionComponents = await _db.InstanceSectionComponents
                                                         .Include(x => x.Component)
                                                            .ThenInclude(x => x.TemplateFields)
                                                         .Where(x => instanceSectionComponentIds.Contains(x.Id))
                                                         .ToListAsync(token);

                foreach (var instanceSectionComp in instanceSectionComponentsIn)
                {
                    var existingSectionComponent = instanceSectionComponents.Where(x => x.Id == instanceSectionComp.Id).FirstOrDefault();

                    if (existingSectionComponent != null)
                    {
                        existingSectionComponent.InstanceSectionId = instanceSectionComp.InstanceSectionId;

                        existingSectionComponent.Component.BuilderRowNumber = instanceSectionComp.BuilderRowNumber;
                        existingSectionComponent.Component.TemplateFields.ColNumber = instanceSectionComp.ColNumber;
                    }

                    var instanceSections = await _db.InstanceSections.Where(x => x.Id == instanceSectionComp.InstanceSectionId).FirstOrDefaultAsync(token);

                    var instance = await _db.Instances.Where(x => x.Id == instanceSections.InstanceId).FirstOrDefaultAsync(token);
                    instance.LastModifiedDate = DateTime.UtcNow;
                    instance.LastModifiedBy = userId;
                }

                await _db.SaveChangesAsync(token);

                return true;
            }

            return false;
        }

        public async Task PersistSectionToInstances(Section section, CancellationToken token)
        {
            await _db.LoadStoredProc("PersistSectionToInstances")
                .WithSqlParam("@SectionId", section.Id)
                .WithSqlParam("@TemplateId", section.TemplateId)
                .WithSqlParam("@SortOrder", section.SortOrder)
                .ExecuteStoredProcAsync(token);
        }

        public async Task PersistComponentToInstances(Component component, Guid sectionId, Guid userId, CancellationToken token)
        {
            await _db.LoadStoredProc("PersistComponentToInstances")
                .WithSqlParam("@ComponentId", component.Id)
                .WithSqlParam("@SectionId", sectionId)
                .WithSqlParam("@UserId", userId)
                .ExecuteStoredProcAsync(token);
        }

        public async Task<InstanceSectionComponentResult> UpdateInstanceSectionComponent(Guid userId, Guid instanceSectionComponentId, InstanceSectionComponentValue instanceSectionComponentValue, CancellationToken token)
        {
            var instanceSectionComponent = await _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component)
                    .ThenInclude(x => x.ComponentType)
                .Include(x => x.InstanceSectionComponentRows)
                .FirstOrDefaultAsync(x => x.Id == instanceSectionComponentId, token);

            if (instanceSectionComponent != null)
            {
                if (instanceSectionComponent.Component.ComponentType.Name.ToLower() == "row manager")
                {
                    if (instanceSectionComponent.InstanceSectionComponentRows != null && instanceSectionComponent.InstanceSectionComponentRows.Any())
                    {
                        _db.InstanceSectionComponentRows.RemoveRange(instanceSectionComponent.InstanceSectionComponentRows);
                    }

                    var rows = instanceSectionComponentValue.value.Deserialize<List<InstanceRowLite>>();
                    foreach (var instanceRowLite in rows)
                    {
                        _db.InstanceSectionComponentRows.Add(new InstanceSectionComponentRows
                        {
                            InstanceSectionComponentId = instanceSectionComponent.Id,
                            RowId = instanceRowLite.Id,
                            Hidden = false,
                            SortOrder = instanceRowLite.SortOrder ?? 0
                        });
                    }
                }
                else
                {
                    instanceSectionComponent.Value = instanceSectionComponentValue.value;
                }

                var instance = await _db.Instances.Where(x => x.Id == instanceSectionComponent.InstanceSection.InstanceId).FirstOrDefaultAsync(token);
                instance.LastModifiedDate = DateTime.UtcNow;
                instance.LastModifiedBy = userId;

                await _db.SaveChangesAsync(token);

                var instanceId = instanceSectionComponent.InstanceSection.InstanceId;
                await _db.LoadStoredProc("InstanceUpdateKeywords")
                         .WithSqlParam("@instanceId", instanceId)
                         .ExecuteStoredProcAsync(token);

                var existingInstance = await _db.Instances
                    .Include(x => x.Feature)
                        .ThenInclude(x => x.FeatureType)
                    .Include(x => x.InstanceUsers)
                    .Where(x => x.Id == instanceSectionComponent.InstanceSection.InstanceId)
                    .FirstOrDefaultAsync(token);

                return new InstanceSectionComponentResult(instanceSectionComponent.Id, new Component(instanceSectionComponent.Component), new Instance(existingInstance, userId), existingInstance.InstanceUsers.Select(x => x.UserId).ToList());
            }

            return null;
        }
        public async Task<List<Component>?> GetDynamicSectionComponentTypes(Guid sectionId)
        {
            var section = await _db.Sections
                .Include(x => x.Components)
                    .ThenInclude(x => x.ComponentType)
                .Include(x => x.Components)
                    .ThenInclude(x => x.TemplateFields)
                .Where(x => x.Id == sectionId)
                .FirstOrDefaultAsync();

            if (section == null)
            {
                return null;
            }

            return section.Components.Where(x => x.IsLocked != true).Select(x => new Component(x)).ToList();
        }

        public async Task<InstanceSectinComponentAddResult> AddInstanceSectionComponents(Guid instanceSectionId, List<Guid> componentIds)
        {
            var defaultComponents = await _db.Components
                .Include(x => x.TemplateFields)
                .Include(x => x.Rows)
                .Include(x => x.Question)
                .Where(x => componentIds.Contains(x.Id))
                .ToListAsync();

            var instanceSection = await _db.InstanceSections.Where(x => x.Id == instanceSectionId).FirstOrDefaultAsync();

            var existingSectionComponents = await GetInstanceSectionComponentsIQueryable(new List<Guid>() { instanceSectionId }, instanceSection.InstanceId)
                .Where(x => x.IsDeleted != true)
                .ToListAsync();

            var max = existingSectionComponents.Select(x => x.Component.BuilderRowNumber).Max();
            var startRow = max != null ? max + 1 : 0;

            if (!componentIds.Any())
            {
                return null;
            }

            var result = new InstanceSectinComponentAddResult(new List<InstanceSectionComponents>(), new List<RowIn>());

            foreach (var comp in defaultComponents)
            {
                var newComp = new Components()
                {
                    Id = Guid.NewGuid(),
                    ComponentTypeId = comp.ComponentTypeId,
                    BuilderSortOrder = comp.BuilderSortOrder,
                    BuilderRowNumber = startRow,
                    HoverSortOrder = comp.HoverSortOrder,
                    InstanceSortOrder = comp.InstanceSortOrder,
                };

                if (comp.QuestionId != null)
                {
                    var newQuestion = new Questions()
                    {
                        Id = Guid.NewGuid(),
                        Title = comp.Question.Title,
                        Category = comp.Question.Category,
                        Runtime = comp.Question.Runtime,
                        QuestionTypeId = comp.Question.QuestionTypeId,
                        QuestionText = comp.Question.QuestionText,
                        QuestionDescription = comp.Question.QuestionDescription,
                        Weighting = comp.Question.Weighting,
                        Required = comp.Question.Required,
                        SortOrder = comp.Question.SortOrder,
                        MeasuredTagId = comp.Question.MeasuredTagId,
                        IsDynamicOptions = comp.Question.IsDynamicOptions,
                        IsAuthorEditable = comp.Question.IsAuthorEditable,
                        IsRandom = comp.Question.IsRandom,
                        IsOtherOption = comp.Question.IsOtherOption,
                        IsLimitTo = comp.Question.IsLimitTo,
                        Limit = comp.Question.Limit,
                        IsOptionPhoto = comp.Question.IsOptionPhoto,
                        ScaleStart = comp.Question.ScaleStart,
                        ScaleEnd = comp.Question.ScaleEnd,
                        ScaleLabelOne = comp.Question.ScaleLabelOne,
                        ScaleLabelTwo = comp.Question.ScaleLabelTwo,
                        ScaleLabelThree = comp.Question.ScaleLabelThree,
                        BackgroundImageAssetId = comp.Question.BackgroundImageAssetId,
                        Purpose = comp.Question.Purpose,
                        DynamicOptionsParentId = comp.Question.DynamicOptionsParentId,
                        FeatureId = comp.Question.FeatureId,
                        AnswerText = comp.Question.AnswerText
                    };

                    _db.Questions.Add(newQuestion);

                    newComp.QuestionId = newQuestion.Id;
                }

                if (comp.IsLocked == true)
                {
                    newComp.IsLocked = true;
                    newComp.ParentComponentId = comp.Id;
                    newComp.BuilderRowNumber = comp.BuilderRowNumber;
                }

                _db.Components.Add(newComp);

                if (comp.Rows.Any())
                {
                    var row = comp.Rows.FirstOrDefault();
                    result.Rows.Add(new RowIn
                    {
                        Id = Guid.NewGuid(),
                        Title = row.Title,
                        TitleHtmltag = row.TitleHtmltag,
                        Description = row.Description,
                        DescriptionHtmltag = row.DescriptionHtmltag,
                        RowTypeId = row.RowTypeId,
                        Alignment = row.Alignment,
                        CourouselFlag = row.CourouselFlag,
                        ThumbnailTypeId = row.ThumbnailTypeId,
                        ComponentId = newComp.Id,
                        Status = row.Status,
                        Filter = row.Filter,
                        Show = row.Show
                    });
                }

                _db.TemplateFields.Add(new TemplateFields()
                {
                    Id = Guid.NewGuid(),
                    ComponentId = newComp.Id,
                    Label = comp.TemplateFields.Label,
                    PlaceHolderText = comp.TemplateFields.PlaceHolderText,
                    HelpTitle = comp.TemplateFields.HelpTitle,
                    HelpDescription = comp.TemplateFields.HelpDescription,
                    ToolTip = comp.TemplateFields.ToolTip,
                    ButtonText = comp.TemplateFields.ButtonText,
                    DefaultImageUrl = comp.TemplateFields.DefaultImageUrl,
                    DefaultText = comp.TemplateFields.DefaultText,
                    TagTreeId = comp.TemplateFields.TagTreeId,
                    IsRequiredField = comp.TemplateFields.IsRequiredField,
                    IsPreviewField = comp.TemplateFields.IsPreviewField,
                    IsBuilderEnabled = comp.TemplateFields.IsBuilderEnabled,
                    IsInherit = comp.TemplateFields.IsInherit,
                    DropDownValues = comp.TemplateFields.DropDownValues,
                    SystemPropertyId = comp.TemplateFields.SystemPropertyId,
                    IsHoverField = comp.TemplateFields.IsHoverField,
                    IsVariable = comp.TemplateFields.IsVariable,
                    LimitTo = comp.TemplateFields.LimitTo,
                    IsViewField = comp.TemplateFields.IsViewField,
                    IsFilter = comp.TemplateFields.IsFilter,
                    IsSrcDevice = comp.TemplateFields.IsSrcDevice,
                    IsSrcRepository = comp.TemplateFields.IsSrcRepository,
                    IsSrcEmbedCode = comp.TemplateFields.IsSrcEmbedCode,
                    IsTag = comp.TemplateFields.IsTag,
                    IsVisibleRepository = comp.TemplateFields.IsVisibleRepository,
                    FileTypeBw = comp.TemplateFields.FileTypeBw,
                    MinFileSize = comp.TemplateFields.MinFileSize,
                    MaxFileSize = comp.TemplateFields.MaxFileSize,
                    UpgradeMessage = comp.TemplateFields.UpgradeMessage,
                    ParentIdSystemPropertyLink = comp.TemplateFields.ParentIdSystemPropertyLink,
                    IsParentSystemPropertyLinkField = comp.TemplateFields.IsParentSystemPropertyLinkField,
                    DropDownLinkType = comp.TemplateFields.DropDownLinkType,
                    OpenExternal = comp.TemplateFields.OpenExternal,
                    IsBlockRequired = comp.TemplateFields.IsBlockRequired,
                    IsAuthorRequired = comp.TemplateFields.IsAuthorRequired,
                    PercentageToComplete = comp.TemplateFields.PercentageToComplete,
                    CommunicationBlockId = comp.TemplateFields.CommunicationBlockId,
                    Colspan = comp.TemplateFields.Colspan,
                    ColNumber = comp.TemplateFields.ColNumber,
                    IconAssetId = comp.TemplateFields.IconAssetId,
                    BackgroundAssetId = comp.TemplateFields.BackgroundAssetId,
                    Label1 = comp.TemplateFields.Label1,
                    PlaceHolder1 = comp.TemplateFields.PlaceHolder1,
                    Default1 = comp.TemplateFields.Default1,
                    Caption1 = comp.TemplateFields.Caption1,
                    Description1 = comp.TemplateFields.Description1,
                    Label2 = comp.TemplateFields.Label2,
                    PlaceHolder2 = comp.TemplateFields.PlaceHolder2,
                    Default2 = comp.TemplateFields.Default2,
                    Caption2 = comp.TemplateFields.Caption2,
                    Description2 = comp.TemplateFields.Description2,
                    Label3 = comp.TemplateFields.Label3,
                    PlaceHolder3 = comp.TemplateFields.PlaceHolder3,
                    Default3 = comp.TemplateFields.Default3,
                    Caption3 = comp.TemplateFields.Caption3,
                    Description3 = comp.TemplateFields.Description3,
                    Label4 = comp.TemplateFields.Label4,
                    PlaceHolder4 = comp.TemplateFields.PlaceHolder4,
                    Default4 = comp.TemplateFields.Default4,
                    Caption4 = comp.TemplateFields.Caption4,
                    Description4 = comp.TemplateFields.Description4,
                    HeightPx = comp.TemplateFields.HeightPx,
                    ShowGradient = comp.TemplateFields.ShowGradient,
                    CustomGradient = comp.TemplateFields.CustomGradient,
                    MoveToBack = comp.TemplateFields.MoveToBack,
                    AspectRatio = comp.TemplateFields.AspectRatio,
                    IsCoverImage = comp.TemplateFields.IsCoverImage,
                    IsRetake = comp.TemplateFields.IsRetake
                });

                var instanceComp = new InstanceSectionComponents()
                {
                    Id = Guid.NewGuid(),
                    InstanceSectionId = instanceSectionId,
                    ComponentId = newComp.Id,
                    Value = null
                };

                result.InstanceSectionComponents.Add(instanceComp);

                _db.InstanceSectionComponents.Add(instanceComp);
            }

            await _db.SaveChangesAsync();
            return result;
        }

        public async Task<bool> MarkInstanceSectionAsDeleted(Guid instanceSectionId, Guid userId)
        {
            var instanceSection = await _db.InstanceSections.Where(x => x.Id == instanceSectionId).FirstOrDefaultAsync();

            if (instanceSection != null)
            {
                instanceSection.IsDeleted = true;
                await _db.SaveChangesAsync();

                List<Guid> rowIds = new List<Guid>();

                var instanceSectionComponentRows = await _db.InstanceSectionComponentRows
                    .Where(x => x.InstanceSectionComponent.InstanceSectionId == instanceSectionId)
                    .Select(x => x.Id)
                    .ToListAsync();

                if (instanceSectionComponentRows.Any())
                {
                    rowIds.AddRange(instanceSectionComponentRows);
                }

                var rows = await _db.Rows.Where(x => x.Component.InstanceSectionComponents.Any(y => y.InstanceSectionId == instanceSectionId)).Select(x => x.Id).ToListAsync();

                if (rows.Any())
                {
                    rowIds.AddRange(rows);
                }

                var instance = await _db.Instances.Where(x => x.Id == instanceSection.InstanceId).FirstOrDefaultAsync();
                instance.LastModifiedDate = DateTime.UtcNow;
                instance.LastModifiedBy = userId;

                var earningRowCriteria = await _db.EarningRowCriteria
                    .Where(x => rowIds.Contains(x.RowId))
                    .ToListAsync();

                _db.EarningRowCriteria.RemoveRange(earningRowCriteria);
                await _db.SaveChangesAsync();

                return true;
            }

            return false;
        }

        public async Task<bool> MarkInstanceSectionComponentAsDeleted(Guid instanceSectionComponentId, Guid userId)
        {
            var instanceSectionComponent = await _db.InstanceSectionComponents.Include(x => x.InstanceSection).Where(x => x.Id == instanceSectionComponentId).FirstOrDefaultAsync();

            if (instanceSectionComponent != null)
            {
                var instance = await _db.Instances.Where(x => x.Id == instanceSectionComponent.InstanceSection.InstanceId).FirstOrDefaultAsync();
                instance.LastModifiedDate = DateTime.UtcNow;
                instance.LastModifiedBy = userId;

                instanceSectionComponent.IsDeleted = true;
                await _db.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<IEnumerable<InstanceSectionComponent>> GetInstanceSectionComponents(Guid instanceSectionId)
        {
            var instanceSection = await _db.InstanceSections.Where(x => x.Id == instanceSectionId).FirstOrDefaultAsync();

            var instanceSectionComponentsSplit = GetInstanceSectionComponentsIQueryable(new List<Guid>() { instanceSectionId }, instanceSection.InstanceId).Where(x => x.IsDeleted != true).AsSplitQuery();

            var instanceSectionComponents = await instanceSectionComponentsSplit
                                      .OrderBy(x => x.Component.BuilderRowNumber)
                                      .ThenBy(x => x.Component.BuilderSortOrder)
                                      .ToListAsync();

            var instanceComponentTagIds = instanceSectionComponents.Where(x => x.Component.ComponentType.Name == "Dropdown" && x.Component.TemplateFields.DropDownLinkType.Title == "Tags" && x.Component.TemplateFields.IsTag != true).Select(x => x.Value.ToGUIDNull()).ToList();
            var tags = await GetInstanceComponentLinkedTags(instanceComponentTagIds);

            return instanceSectionComponents.Select(x => new InstanceSectionComponent(x, instanceSection.InstanceId, null, tags, true));
        }

        public async Task<List<VwInstanceProgressGrade>?> GetInstancesGradingInfo(List<Guid> instanceIds)
        {
            var res = await _db.VwInstanceProgressGrade
                .Where(x => instanceIds.Contains((Guid)x.Id))
                .ToListAsync();

            return res;
        }
        public async Task<List<Tags>> GetInstanceComponentLinkedTags(List<Guid?> instanceComponentTags)
        {
            var tags = await _db.Tags.Where(x => instanceComponentTags.Contains(x.Id) || instanceComponentTags.Contains(x.ParentId)).Select(x => x.Id).ToListAsync();

            var childTags = await _db.TagLinks
                    .Include(x => x.ChildTag)
                .Where(x => tags.Contains(x.ParentTagId))
                .Select(x => x.ChildTag)
                .AsSplitQuery()
                .ToListAsync();

            return childTags;
        }

        public async Task<string?> GetInstanceOrgLocation(Guid instanceId, CancellationToken token)
        {
            var orgLocation = await _db.Instances.Include(x => x.Organization).Where(x => x.Id == instanceId).Select(x => x.Organization.Country).FirstOrDefaultAsync(token);
            return orgLocation;
        }
        public async Task<bool> GetInstanceStatusById(Guid instanceId, CancellationToken token)
        {
            var instancePublished = await _db.Instances
                                    .Where(x => x.Id == instanceId && x.Status != "private" && x.Status != "Deleted")
                                    .AnyAsync(token);
            return instancePublished;
        }

        public async Task<bool> GetInstanceStatusBySlug(string slug, CancellationToken token)
        {
            var instancePublished = await _db.Instances
                .Where(x => x.Status != "private" && x.Status != "Deleted" && ((x.Feature.Slug == slug && x.IsDefault == true) || x.Slug == slug))
                .AnyAsync(token);

            return instancePublished;
        }
        public async Task<MyInstancesResult?> GetInstanceAssignments(Guid instanceId, bool onlyAssignments = false)
        {
            var assignmentsIQueryable = _db.InstanceAssignments
                .Include(x => x.Instance)
                    .ThenInclude(x => x.InstanceAssignmentsParentInstance)
                        .ThenInclude(x => x.Instance)
                .Where(x => x.ParentInstanceId == instanceId && x.Instance.Status != "deleted")
                .AsQueryable();

            if (onlyAssignments == true)
            {
                assignmentsIQueryable = assignmentsIQueryable.Where(x => x.Instance.Feature.FeatureType.Name == "Accredited Learning Container Pages");
            }

            var assignments = await assignmentsIQueryable.ToListAsync();

            if (assignments == null)
            {
                return null;
            }

            return new MyInstancesResult(assignments.Select(x => new Instance(x.Instance)), assignments.SelectMany(x => x.Instance.InstanceAssignmentsParentInstance.Select(y => new Instance(y.Instance))));
        }

        public async Task<bool> AddInstanceAssignment(Guid parentInstanceId, Guid instanceId, Guid userId)
        {
            _db.InstanceAssignments.Add(new InstanceAssignments()
            {
                Id = Guid.NewGuid(),
                ParentInstanceId = parentInstanceId,
                InstanceId = instanceId
            });

            await _db.SaveChangesAsync();

            await this.SyncParentAndChildUsers(userId, parentInstanceId);

            return true;
        }

        public async Task<bool> MoveNonClassRoomContent(CancellationToken token)
        {
            var instanceToUpdateIds = await _db.AssignmentInstancesToUpdate.Where(x => x.IsComplete != true).Select(x => x.Id).ToListAsync();

            var assignmentsIQueryable = _db.InstanceAssignments
                    .Include(x => x.ParentInstance)
                        .ThenInclude(x => x.Feature)
                            .ThenInclude(x => x.FeatureType)
                    .Include(x => x.Instance)
                        .ThenInclude(x => x.Feature)
                            .ThenInclude(x => x.FeatureType)
                    .Where(x => x.ParentInstance.Feature.FeatureType.Name == "Modifiable Learning Container Pages" && instanceToUpdateIds.Contains((Guid)x.ParentInstanceId) && x.Instance.Title != x.ParentInstance.Title)
                    .AsQueryable();

            var assignments = await assignmentsIQueryable
                .Where(x => x.Instance.Feature.FeatureType.Name != "Accredited Learning Container Pages")
                .ToListAsync();

            var featureId = await _db.Features
                .Where(x => x.Title == "Assignment Pages")
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (assignments != null)
            {
                var parentInstances = assignments.Select(x => x.ParentInstance).DistinctBy(x => x.Id).ToList();

                var ownerIds = parentInstances.Select(x => x.UserId).ToList();

                var orgUsers = await _db.OrganizationUsers
                    .Where(x => ownerIds.Contains(x.UserId))
                    .ToListAsync();

                var defaultOrgId = await _db.Organizations.Where(x => x.Name == "Edge Factor - Master").Select(x => x.Id).FirstOrDefaultAsync();

                var parentInstanceIds = parentInstances.Select(x => x.Id).ToList();

                var allCurrentAssignments = await _db.InstanceAssignments
                                    .Include(x => x.ParentInstance)
                                        .ThenInclude(x => x.InstanceSections)
                                            .ThenInclude(x => x.InstanceSectionComponents)
                                                .ThenInclude(x => x.InstanceSectionComponentRows)
                                                    .ThenInclude(x => x.Row)
                                                .ThenInclude(x => x.RowContents)
                                    .Where(x => parentInstanceIds.Contains((Guid)x.ParentInstanceId) == true && x.Instance.Feature.FeatureType.Name != "Accredited Learning Container Pages").ToListAsync();

                foreach (var parent in parentInstances)
                {
                    Console.WriteLine(parentInstances.FindIndex(x => x.Id == parent.Id) + " out of " + parentInstances.Count);

                    var orgId = parent.OrganizationId;

                    if (orgId == null || orgId == Guid.Empty)
                    {
                        orgId = orgUsers.Where(x => x.UserId == parent.UserId).Select(x => x.OrganizationId).FirstOrDefault();

                        if (orgId == null)
                        {
                            orgId = defaultOrgId;
                        }

                        parent.OrganizationId = orgId;
                    }

                    if (parent.UserId == null || parent.UserId == Guid.Empty)
                    {
                        parent.UserId = Guid.Parse("a9ff643b-0359-44a1-9335-32a9b24087fe");
                    }

                    if (parent != null && parent.UserId != null && parent.OrganizationId != null)
                    {
                        var currentAssignments = allCurrentAssignments.Where(x => x.ParentInstanceId == parent.Id).ToList();

                        List<InstanceSectionComponentRows> newInstances = currentAssignments.SelectMany(x => x.ParentInstance.InstanceSections.SelectMany(y => y.InstanceSectionComponents.SelectMany(z => z.InstanceSectionComponentRows))).Distinct().ToList();

                        foreach (var newInstance in newInstances)
                        {
                            Instance instanceIn = new Instance(newInstance.Row.Title, featureId, (Guid)orgId);

                            var instance = await CreateInstance((Guid)parent.UserId, instanceIn, token);

                            await UpdateInstanceStatus(instance.Id, "public", (Guid)parent.UserId, newInstance.Row.DueDate, token);

                            var instanceIds = newInstance.Row.RowContents.Select(x => x.ContentId).ToList();

                            if (instanceIds == null)
                            {
                                continue;
                            }
                            ;

                            var newInstanceAssignments = await _db.InstanceAssignments.Where(x => x.ParentInstanceId == parent.Id && x.Instance.Feature.FeatureType.Name != "Accredited Learning Container Pages" && instanceIds.Any(y => y == x.InstanceId)).ToListAsync();

                            foreach (var item in newInstanceAssignments)
                            {
                                item.ParentInstanceId = instance.Id;
                            }

                            _db.InstanceAssignments.Add(new InstanceAssignments()
                            {
                                Id = Guid.NewGuid(),
                                ParentInstanceId = parent.Id,
                                InstanceId = instance.Id
                            });

                            await _db.SaveChangesAsync();
                        }

                        var toUpdateRecord = await _db.AssignmentInstancesToUpdate.Where(x => x.Id == parent.Id).FirstOrDefaultAsync();
                        toUpdateRecord.IsComplete = true;

                        await _db.SaveChangesAsync();
                    }
                }
            }

            return true;
        }

        public async Task<bool> DeleteInstanceAssignment(Guid instanceId, Guid parentInstanceId)
        {
            var assignment = await _db.InstanceAssignments.Where(x => x.InstanceId == instanceId && x.ParentInstanceId == parentInstanceId).FirstOrDefaultAsync();

            if (assignment == null)
            {
                return false;
            }

            _db.InstanceAssignments.Remove(assignment);
            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<bool> SyncParentAndChildUsers(Guid userId, Guid parentInstanceId)
        {
            var parentInstance = await _db.Instances
                .Include(x => x.Feature)
                    .ThenInclude(x => x.FeatureType)
                .Include(x => x.InstanceUsers)
                .Where(x => x.Id == parentInstanceId).FirstOrDefaultAsync();

            if (parentInstance != null)
            {
                var childInstances = await _db.Instances
                    .Include(x => x.InstanceAssignmentsInstance)
                    .Include(x => x.InstanceUsers)
                    .Where(x => x.InstanceAssignmentsInstance.Any(y => y.ParentInstanceId == parentInstance.Id)).ToListAsync();

                var parentInstanceUsers = parentInstance.InstanceUsers.ToList();
                var childInstanceUsers = childInstances.SelectMany(x => x.InstanceUsers).DistinctBy(x => x.UserId).ToList();

                foreach (var childInstance in childInstances)
                {
                    var newChildInstanceUsers = parentInstanceUsers.Where(x => !childInstance.InstanceUsers.Any(y => y.UserId == x.UserId && y.ParentInstanceUserId == x.Id)).ToList();

                    if (newChildInstanceUsers.Any())
                    {
                        childInstance.LastModifiedDate = DateTime.UtcNow;
                        childInstance.LastModifiedBy = userId;

                        _db.InstanceUsers.AddRange(newChildInstanceUsers.Select(x => new InstanceUsers() { Id = Guid.NewGuid(), InstanceId = childInstance.Id, UserId = x.UserId, RoleId = x.RoleId, Status = x.Status, ParentInstanceUserId = x.Id }).ToList());
                    }
                }
            }

            await _db.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Organization>> GetOrganizationsByInstanceId(Guid id)
        {
            var organizations = await _db.InstanceOrganizations.Include(x => x.Organization).Where(x => x.InstanceId == id).ToListAsync();
            return organizations.Select(x => new Organization(x.Organization));
        }

        public async Task<bool> AddOrganizationToInstance(Guid instanceId, Guid organizationId)
        {
            var instanceOrganization = new InstanceOrganizations()
            {
                Id = Guid.NewGuid(),
                InstanceId = instanceId,
                OrganizationId = organizationId
            };

            _db.InstanceOrganizations.Add(instanceOrganization);
            await _db.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveOrganizationFromInstance(Guid instanceId, Guid organizationId)
        {
            var instanceOrganization = await _db.InstanceOrganizations.Where(x => x.OrganizationId == organizationId && x.InstanceId == instanceId).FirstOrDefaultAsync();
            if (instanceOrganization != null)
            {
                _db.InstanceOrganizations.Remove(instanceOrganization);
                await _db.SaveChangesAsync();
                return true;
            }
            return false;
        }
        #endregion
    }
}
